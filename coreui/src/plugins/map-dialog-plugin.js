import MapDialog from '../components/common/MapDialog.vue';

const MapDialogPlugin = {
  install(Vue, options = {}) {
    // Create a constructor for the map dialog component
    const MapDialogConstructor = Vue.extend(MapDialog);

    // Create a global instance
    let mapDialogInstance = null;

    // Create the $mapDialog method
    Vue.prototype.$mapDialog = {
      /**
       * Open a map dialog with positions
       * @param {string} title - Dialog title
       * @param {Array} positions - Array of position objects with lat/lng
       * @param {Object} options - Dialog options
       * @returns {Promise} Promise that resolves when dialog is closed
       */
      open(title, positions = [], options = {}) {
        // Create instance if it doesn't exist
        if (!mapDialogInstance) {
          // Create the instance without store injection initially
          mapDialogInstance = new MapDialogConstructor();
          mapDialogInstance.$mount();
          document.body.appendChild(mapDialogInstance.$el);
        }

        // Call the open method
        return mapDialogInstance.open(title, positions, options);
      },

      /**
       * Show locations on map
       * @param {string} title - Dialog title
       * @param {Array} locations - Array of location objects
       * @param {Object} options - Dialog options
       * @returns {Promise} Promise that resolves when dialog is closed
       */
      showLocations(title, locations = [], options = {}) {
        // Convert locations to positions format if needed
        const positions = locations.map(location => {
          if (location.lat && location.lng) {
            return location;
          }
          if (location.latitude && location.longitude) {
            return {
              lat: parseFloat(location.latitude),
              lng: parseFloat(location.longitude),
              ...location
            };
          }
          return location;
        });

        return this.open(title, positions, {
          icon: 'location',
          color: 'info',
          width: 900,
          ...options
        });
      },

      /**
       * Show visit locations on map
       * @param {string} title - Dialog title
       * @param {Array} visits - Array of visit objects
       * @param {Object} options - Dialog options
       * @returns {Promise} Promise that resolves when dialog is closed
       */
      showVisits(title, visits = [], options = {}) {
        const positions = visits.map(visit => ({
          lat: parseFloat(visit.latitude || visit.lat),
          lng: parseFloat(visit.longitude || visit.lng),
          visit_id: visit.id || visit.visit_id,
          account: visit.account_name || visit.account,
          doctor: visit.doctor_name || visit.doctor,
          ...visit
        }));

        return this.open(title, positions, {
          icon: 'map',
          color: 'primary',
          width: 1000,
          ...options
        });
      },

      /**
       * Show account locations on map
       * @param {string} title - Dialog title
       * @param {Array} accounts - Array of account objects
       * @param {Object} options - Dialog options
       * @returns {Promise} Promise that resolves when dialog is closed
       */
      showAccounts(title, accounts = [], options = {}) {
        const positions = accounts.map(account => ({
          lat: parseFloat(account.latitude || account.lat),
          lng: parseFloat(account.longitude || account.lng),
          account_id: account.id || account.account_id,
          account: account.name || account.account_name,
          doctor: account.doctor_name || account.doctor,
          manager: account.manager_name || account.manager,
          ...account
        }));

        return this.open(title, positions, {
          icon: 'location',
          color: 'success',
          width: 900,
          ...options
        });
      },

      /**
       * Show division locations on map
       * @param {string} title - Dialog title
       * @param {Array} divisions - Array of division objects
       * @param {Object} options - Dialog options
       * @returns {Promise} Promise that resolves when dialog is closed
       */
      showDivisions(title, divisions = [], options = {}) {
        const positions = divisions.map(division => ({
          lat: parseFloat(division.latitude || division.lat),
          lng: parseFloat(division.longitude || division.lng),
          division_id: division.id || division.division_id,
          name: division.name || division.division_name,
          line: division.line_name || division.line,
          key: 'line_division',
          ...division
        }));

        return this.open(title, positions, {
          icon: 'map',
          color: 'warning',
          width: 800,
          ...options
        });
      }
    };

    // Also make it available as a global property
    Vue.prototype.$globalMapDialog = Vue.prototype.$mapDialog;
  }
};

export default MapDialogPlugin;
