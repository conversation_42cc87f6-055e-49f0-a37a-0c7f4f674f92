<template>
  <div>
    <CCard no-header>
      <CCardBody>
        <div class="row">
          <div class="col">
            <CInput label="Name" type="text" placeholder="Name" v-model="line_division.name"></CInput>
          </div>
          <div class="col">
            <CFormGroup>
              <template #label> Division Type </template>
              <template #input>
                <v-select v-model="line_division.division_type_id" :options="lineDivisionTypes" label="name" :value="0"
                  :reduce="(division_type) => division_type.id" placeholder="Select Division" class="mt-2" />
              </template>
            </CFormGroup>
          </div>
          <div class="col">
            <CFormGroup>
              <template #label> Is Kol </template>
              <template #input>
                <v-select v-model="line_division.is_kol" :options="kolOptions" label="name"
                  :reduce="(is_kol) => is_kol.value" placeholder="Select KOL" class="mt-2" />
              </template>
            </CFormGroup>
          </div>
        </div>
        <div class="row">
          <div class="col">
            <CInput label="From" type="date" placeholder="From" v-model="line_division.from_date"></CInput>
          </div>
          <div class="col">
            <CInput label="To" type="date" placeholder="To" v-model="line_division.to_date"></CInput>
          </div>
        </div>
      </CCardBody>
      <CCardFooter>
        <CButton color="primary" v-if="!lineDivisionIsEditing" @click="store()">Create</CButton>
        <CButton color="primary" v-if="lineDivisionIsEditing" @click="update()">Update</CButton>
        <CButton color="default" :to="{ name: 'lines' }">Cancel</CButton>
      </CCardFooter>
    </CCard>
    <CDataTable hover striped sorter tableFilter footer itemsPerPageSelect :items="line_divisions"
      :fields="line_divisions_fields" :items-per-page="1000" :active-page="1" :responsive="true" pagination thead-top>
      <template #from_date="{ item }">
        <td>{{ format_date(item.from_date) }}</td>
      </template>
      <template #to_date="{ item }">
        <td>{{ format_date(item.to_date) }}</td>
      </template>
      <template #parent_name="{ item }">
        <td v-if="item.parent_name">{{ item.parent_name }}</td>
        <td v-else-if="!item.parent_name">-No Parent-</td>
      </template>
      <template #is_kol="{ item }">
        <td v-if="item.is_kol == 1">Yes</td>
        <td v-else>No</td>
      </template>
      <template slot="thead-top">
        <td style="border-top: none"><strong>Total</strong></td>
        <td style="border-top: none" class="text-xs-right">
          {{ line_divisions.length }}
        </td>
      </template>
      <template #actions="{ item }">
        <td>
          <div class="row justify-content-center">
            <CButton color="success" class="text-white btn-sm mt-2 mr-1" v-if="checkPermission('delete_line_divisions')"
              @click="edit(item.id)"><i class="cil-pencil"></i>
              <CIcon name="cil-pencil" />
            </CButton>
            <c-button color="danger" v-if="checkPermission('delete_line_divisions')" class="btn-sm mt-2 mr-1" @click="
              $dialog.open('Delete', 'Do you want to delete this record?', {
                  color: 'danger'
                })
                .then((confirmed) => {
                  if (confirmed) {
                    deleteLineDivision(item);
                  }
                })
              "><c-icon name="cil-trash" /></c-button>
            <c-button color="primary" class="btn-sm mt-2 mr-1" @click="getLocation(item)" v-if="item.ll != null && item.lg != null">
              <CIcon class="text-white" name="marker" />
            </c-button>
          </div>
        </td>
      </template>
    </CDataTable>
  </div>
</template>
<script>
import moment from "moment";

import vSelect from "vue-select";
export default {
  components: {
    vSelect,
  },
  data() {
    return {
      fromDate: new Date().toISOString().slice(0, 10),
      line_divisions: [],
      divisionparents: [],
      lineDivisionIsEditing: false,
      divisions: [],
      kolOptions: [
        { value: 0, name: "NO" },
        { value: 1, name: "YES" },
      ],
      line_division: {
        id: null,
        line_id: 0,
        is_kol: null,
        division_type_id: "",
        name: "",
        from_date: "",
        to_date: "",
      },
      line_divisions_fields: [
        "id",
        "line name",
        "division type name",
        "name",
        "parent_name",
        "is_kol",
        "from_date",
        "to_date",
        "actions",
      ],
    };
  },
  props: {
    lineDivisionTypes: {
      type: Array,
      required: true,
    },
    line_id: {
      type: Number,
      required: true,
    },
  },
  emits: ["getLineDivisions", "getLastLevelLineDivisions"],
  methods: {
    getLocation(item) {
      axios
        .get(`/api/division-location/${item.id}`)
        .then((response) => {
          this.location = response.data.data;
          if (
            this.location.position.lat != 0 &&
            this.location.position.lng != 0
          ) {
            this.$mapDialog.open(`Map of Division id: ${this.location.id}`, [
              this.location.position,
            ]);
          } else {
            this.flash("There is no loaction for this Division");
          }
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    format_date: function (value) {
      let date_format = localStorage.getItem("date_format");
      if (value) {
        return moment(String(value)).format(date_format);
      }
    },
    edit_date_format: function (value) {
      if (value) {
        return moment(String(value)).format("YYYY-MM-DD");
      }
    },
    getLineDivisions() {
      axios
        .get(`/api/lines/${this.line_id}/divisions`)
        .then((response) => {
          this.line_divisions = response.data.line_divisions;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    sendDivisionParents() {
      axios
        .get(`/api/lines/${this.line_id}/divisions-parent`)
        .then((response) => {
          this.divisionparents = Object.entries(
            response.data.divisionparents
          ).map(([key, value]) => {
            return { id: value.id, name: value.name };
          });
          this.divisions = Object.entries(response.data.divisions).map(
            ([key, value]) => {
              return { id: value.id, name: value.name };
            }
          );
          this.$emit("getLineDivisions", {
            divisionparents: this.divisionparents,
          });
          this.$emit("getLastLevelLineDivisions", {
            divisions: this.divisions,
          });
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    reset() {
      this.line_division = {
        line_id: this.line_id,
        division_type_id: "",
        name: "",
        from_date: "",
        to_date: "",
      };
    },
    store() {
      axios
        .post(`/api/lines/${this.line_id}/divisions`, {
          line_id: this.line_id,
          division_type_id: this.line_division.division_type_id,
          is_kol: this.line_division.is_kol,
          name: this.line_division.name,
          from_date: this.crmDateFormat(this.line_division.from_date),
          to_date: this.line_division.to_date
            ? this.crmDateFormat(this.line_division.to_date)
            : "",
        })
        .then((response) => {
          this.reset();
          this.sendDivisionParents();
          this.flash("Line Division Created Successfully");
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
      //Refresh table
      this.getLineDivisions();
    },
    edit(id) {
      axios
        .get(`/api/lines/divisions/${id}`)
        .then((response) => {
          this.lineDivisionIsEditing = true;
          this.line_division.id = response.data.line_division.id;
          this.line_division.name = response.data.line_division.name;
          this.line_division.is_kol = response.data.line_division.is_kol;
          this.line_division.line_id = response.data.line_division.line_id;
          this.line_division.division_type_id =
            response.data.line_division.division_type_id;
          this.line_division.from_date = this.edit_date_format(
            response.data.line_division.from_date
          );
          this.line_division.to_date = this.edit_date_format(
            response.data.line_division.to_date
          );
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    update() {
      axios
        .put(`/api/lines/${this.line_id}/divisions/${this.line_division.id}`, {
          id: this.line_division.id,
          name: this.line_division.name,
          line_id: this.line_division.line_id,
          is_kol: this.line_division.is_kol,
          division_type_id: this.line_division.division_type_id,
          from_date: this.crmDateFormat(this.line_division.from_date),
          to_date: this.line_division.to_date
            ? this.crmDateFormat(this.line_division.to_date)
            : "",
        })
        .then((response) => {
          this.lineDivisionIsEditing = false;
          this.reset();
          this.sendDivisionParents();
          this.flash("Line Division Updated Successfully");
          this.getLineDivisions();
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    removeLineDivision(item) {
      const index = this.line_divisions.findIndex((type) => item.id == type.id);
      this.line_divisions.splice(index, 1);
    },
    deleteLineDivision(item) {
      axios
        .delete(`/api/lines-divisions/${item.id}`)
        .then((res) => {
          this.removeLineDivision(item);
          this.flash("Line Division Deleted Successfully");
          this.sendDivisionParents();
        })
        .catch((err) => this.showErrorMessage(err));
    },
  },
  created() {
    this.getLineDivisions();
    this.sendDivisionParents();
  },
};
</script>
