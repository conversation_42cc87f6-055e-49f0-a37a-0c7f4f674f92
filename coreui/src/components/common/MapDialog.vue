<template>
  <transition name="dialog" appear>
    <div v-if="dialog" class="dialog-overlay" :class="{ 'dialog-overlay--dark': darkMode }"
      :style="{ zIndex: options.zIndex }" @click="onOverlayClick" @keydown.esc="cancel" tabindex="-1" ref="overlay">
      <div class="dialog-card dialog-card--map" :class="{ 'dialog-card--dark': darkMode }" :style="{ maxWidth: options.width + 'px' }">
        <!-- Header with gradient background -->
        <div class="dialog-header" :class="[
          { 'dialog-header--dark': darkMode },
          `dialog-header--${options.color}`
        ]">
          <div class="dialog-icon" v-if="options.icon">
            <svg viewBox="0 0 24 24" width="24" height="24">
              <path :d="getIconPath(options.icon)" fill="currentColor" />
            </svg>
          </div>
          <h3 class="dialog-title">{{ title }}</h3>
          <button v-if="options.closable" @click="cancel" class="dialog-close"
            :class="{ 'dialog-close--dark': darkMode }">
            <svg viewBox="0 0 24 24" width="20" height="20">
              <path
                d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"
                fill="currentColor" />
            </svg>
          </button>
        </div>

        <!-- Map Content Section -->
        <div class="dialog-content dialog-content--map" :class="{ 'dialog-content--dark': darkMode }">
          <gmap-map v-if="positions.length > 0" :center="center" :zoom="18" class="map-container">
            <gmap-cluster>
              <gmap-info-window :options="infoOptions" :position="infoWindowPos" :opened="infoWinOpen"
                @closeclick="infoWinOpen = false">
                <div v-html="infoOptions.content"></div>
              </gmap-info-window>
              <gmap-marker v-for="(position, key) in positions" :key="key" :position="position" :clickable="true"
                @click="toggleInfoWindow(position, key)" />
            </gmap-cluster>
          </gmap-map>
          <div v-else class="error-message">{{ message }}</div>
        </div>

        <!-- Actions Section -->
        <div class="dialog-actions" :class="{ 'dialog-actions--dark': darkMode }">
          <button @click="cancel" class="dialog-btn dialog-btn--secondary"
            :class="{ 'dialog-btn--dark': darkMode }">
            {{ options.cancelText }}
          </button>
        </div>
      </div>
    </div>
  </transition>
</template>

<script>
import { mapActions } from "vuex";
import { storePromise } from "../../store/";

export default {
  name: 'MapDialog',
  data() {
    return {
      center: { lat: 30.0444, lng: 31.2357 },
      dialog: false,
      resolve: null,
      reject: null,
      positions: [],
      title: null,
      message: null,
      storeInstance: null, // Cache the store instance
      options: {
        color: "primary",
        width: 800,
        zIndex: 1000000000,
        cancelText: 'Close',
        icon: 'map',
        closable: true
      },
      infoWindowPos: null,
      infoWinOpen: true,
      currentMidx: null,
      infoOptions: {
        content: "",
        //optional: offset infowindow so it visually sits nicely on top of our marker
        pixelOffset: {
          width: 0,
          height: -35,
        },
      },
    };
  },
  computed: {
    darkMode() {
      // Check if store is available through component context or cached instance
      const store = this.$store || this.storeInstance;
      return store ? store.getters['app/darkMode'] : false;
    }
  },
  async mounted() {
    // Initialize store access if not available through component context
    if (!this.$store) {
      try {
        this.storeInstance = await storePromise;
        // Force reactivity update
        this.$forceUpdate();
      } catch (error) {
        console.warn('MapDialog: Failed to initialize store access:', error);
      }
    }

    // Focus management when dialog opens
    this.$watch('dialog', (newVal) => {
      if (newVal) {
        this.$nextTick(() => {
          if (this.$refs.overlay) {
            this.$refs.overlay.focus();
          }
        });
      }
    });
  },
  methods: {
    open(title, positions, options = {}) {
      this.dialog = true;
      this.title = title;
      this.options = { ...this.options, ...options };

      if (positions && positions.length > 0) {
        this.positions = positions;
        this.center = this.positions[0];
      } else {
        this.message = "No location data available";
        this.positions = [];
      }

      return new Promise((resolve, reject) => {
        this.resolve = resolve;
        this.reject = reject;
      });
    },
    agree() {
      if (this.resolve) {
        this.resolve(true);
      }
      this.close();
    },
    cancel() {
      if (this.resolve) {
        this.resolve(false);
      }
      this.close();
    },
    close() {
      this.dialog = false;
      this.resolve = null;
      this.reject = null;
    },
    onOverlayClick(event) {
      if (event.target === event.currentTarget && this.options.closable) {
        this.cancel();
      }
    },
    getIconPath(iconName) {
      const icons = {
        map: 'M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z',
        location: 'M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z',
        info: 'M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z'
      };
      return icons[iconName] || icons.map;
    },
    toggleInfoWindow: function (marker, idx) {
      this.center = marker;
      this.infoWindowPos = marker;
      // this.infoOptions.content = marker.position.id;
      this.infoOptions.content = this.getInfoWindowContent(marker);
      //check if its the same marker that was selected if yes toggle
      if (this.currentMidx == idx) {
        this.infoWinOpen = !this.infoWinOpen;
      }
      //if different marker set infowindow to open and reset current marker index
      else {
        this.infoWinOpen = true;
        this.currentMidx = idx;
      }
    },
    getInfoWindowContent(marker) {

      if (marker.key == 'line_division') {
        return `<div class="">
          <div>
            <div>
              <div class="m-2"><span style="font-weight: bold;">${"division_id:"
          }</span>
              ${marker.division_id}

              </div>
              <div class="m-2"><span style="font-weight: bold;">Line: </span>
                ${marker.line}
              </div>
              <div class="m-2"><span style="font-weight: bold;">Division: </span>
                ${marker.name}
              </div>
            </div>
          </div>
        </div>`;
      } else if (marker.hasOwnProperty('manager')) {
        return `<div class="">
          <div>
            <div>
              <div class="m-2"><span style="font-weight: bold;">${"Manager:"
          }</span>
              ${marker.manager}

              </div>
              <div class="m-2"><span style="font-weight: bold;">Account: </span>
                ${marker.account}
              </div>
              <div class="m-2"><span style="font-weight: bold;">Doctor: </span>
                ${marker.doctor}
              </div>
            </div>
          </div>
        </div>`;
      }
      else {
        return `<div class="">
          <div>
            <div>
              <div class="m-2"><span style="font-weight: bold;">${marker.visit_id ? "visit id: " : "account_id:"
          }</span>
              ${marker.visit_id ? marker.visit_id : marker.account_id}

              </div>
              <div class="m-2"><span style="font-weight: bold;">account: </span>
                ${marker.account}
              </div>
              <div class="m-2"><span style="font-weight: bold;">doctor: </span>
                ${marker.doctor}
              </div>
            </div>
          </div>
        </div>`;
      }

    },
  },
};
</script>

<style scoped>
/* ==============================================
   DIALOG OVERLAY & TRANSITIONS
   ============================================== */

/* Overlay */
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  box-sizing: border-box;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

/* Dark theme overlay */
.dialog-overlay--dark {
  background: rgba(0, 0, 0, 0.8);
}

/* Transition animations */
.dialog-enter-active,
.dialog-leave-active {
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.dialog-enter-active .dialog-card,
.dialog-leave-active .dialog-card {
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.dialog-enter,
.dialog-leave-to {
  opacity: 0;
  backdrop-filter: blur(0px);
}

.dialog-enter .dialog-card,
.dialog-leave-to .dialog-card {
  transform: translateY(-50px) scale(0.9);
  opacity: 0;
}

/* Modal Card */
.dialog-card {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  min-width: 600px;
  max-width: 90vw;
  animation: slideIn 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
}

/* Map dialog specific sizing */
.dialog-card--map {
  min-width: 800px;
  max-height: 90vh;
}

/* Dark theme modal */
.dialog-card--dark {
  background: #2d3748;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.6);
  color: #e2e8f0;
}

@keyframes slideIn {
  from {
    transform: translateY(-50px) scale(0.9);
    opacity: 0;
  }

  to {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
}

/* Header */
.dialog-header {
  padding: 20px 24px;
  display: flex;
  align-items: center;
  gap: 16px;
  position: relative;
  overflow: hidden;
  transition: background 0.3s ease;
}

/* Header gradient backgrounds */
.dialog-header--primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.dialog-header--danger {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  color: white;
}

.dialog-header--success {
  background: linear-gradient(135deg, #51cf66 0%, #40c057 100%);
  color: white;
}

.dialog-header--warning {
  background: linear-gradient(135deg, #ffd43b 0%, #fab005 100%);
  color: #212529;
}

.dialog-header--info {
  background: linear-gradient(135deg, #74c0fc 0%, #339af0 100%);
  color: white;
}

/* Dark theme header adjustments */
.dialog-header--dark {
  border-bottom: 1px solid #4a5568;
}

/* Header icon */
.dialog-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

/* Header title */
.dialog-title {
  flex: 1;
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  line-height: 1.4;
  letter-spacing: -0.025em;
}

/* Close button */
.dialog-close {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 8px;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
}

.dialog-close:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
}

.dialog-close:active {
  transform: scale(0.95);
}

.dialog-close--dark {
  background: rgba(255, 255, 255, 0.1);
}

.dialog-close--dark:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* ==============================================
   CONTENT SECTION
   ============================================== */

/* Content */
.dialog-content {
  padding: 0;
  color: #374151;
  line-height: 1.6;
  transition: color 0.3s ease;
}

.dialog-content--map {
  padding: 0;
  height: 400px;
  position: relative;
}

.dialog-content--dark {
  color: #e2e8f0;
}

/* Map container */
.map-container {
  width: 100%;
  height: 100%;
  border-radius: 0;
}

/* Error message */
.error-message {
  padding: 40px 24px;
  text-align: center;
  color: #6b7280;
  font-size: 16px;
}

.dialog-content--dark .error-message {
  color: #9ca3af;
}

/* ==============================================
   ACTIONS SECTION
   ============================================== */

/* Actions */
.dialog-actions {
  padding: 20px 24px;
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  background: #f9fafb;
  border-top: 1px solid #e5e7eb;
  transition: all 0.3s ease;
}

.dialog-actions--dark {
  background: #374151;
  border-top-color: #4b5563;
}

/* Buttons */
.dialog-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 100px;
  position: relative;
  overflow: hidden;
}

.dialog-btn:focus {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}

.dialog-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Secondary button */
.dialog-btn--secondary {
  background: #6b7280;
  color: white;
}

.dialog-btn--secondary:hover:not(:disabled) {
  background: #4b5563;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(107, 114, 128, 0.4);
}

.dialog-btn--secondary:active {
  transform: translateY(0);
}

.dialog-btn--dark {
  background: #4b5563;
  color: #f3f4f6;
}

.dialog-btn--dark:hover:not(:disabled) {
  background: #374151;
}

/* ==============================================
   RESPONSIVE DESIGN
   ============================================== */

@media (max-width: 768px) {
  .dialog-card--map {
    min-width: 95vw;
    margin: 10px;
  }

  .dialog-content--map {
    height: 300px;
  }

  .dialog-header {
    padding: 16px 20px;
  }

  .dialog-title {
    font-size: 16px;
  }

  .dialog-actions {
    padding: 16px 20px;
    flex-direction: column;
  }

  .dialog-btn {
    width: 100%;
    margin-bottom: 8px;
  }
}

@media (max-width: 480px) {
  .dialog-card--map {
    min-width: 100vw;
    margin: 0;
    border-radius: 0;
    height: 100vh;
  }

  .dialog-content--map {
    height: calc(100vh - 140px);
  }
}

/* ==============================================
   ACCESSIBILITY & PREFERENCES
   ============================================== */

/* High contrast mode */
@media (prefers-contrast: high) {
  .dialog-header--primary {
    background: #000 !important;
  }

  .dialog-header--danger {
    background: #8b0000 !important;
  }

  .dialog-header--success {
    background: #006400 !important;
  }

  .dialog-header--warning {
    background: #ff8c00 !important;
  }

  .dialog-header--info {
    background: #000080 !important;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {

  .dialog-overlay,
  .dialog-card,
  .dialog-btn {
    animation: none !important;
  }

  * {
    transition-duration: 0.1s !important;
  }
}

/* Focus states for accessibility */
.dialog-card:focus-within {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}

.dialog-card--dark:focus-within {
  outline-color: #63b3ed;
}
</style>
