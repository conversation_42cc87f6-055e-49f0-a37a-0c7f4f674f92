<template>
  <div class="container">
    <!-- <div class="main-body"> -->
    <div class="row gutters-sm">
      <div class="col-md-4 mb-3">
        <div class="card">
          <div class="card-body">
            <div class="d-flex flex-column align-items-center text-center">
              <img :src="url" class="rounded-circle" width="150" />
              <div class="mt-3">
                <h4>{{ user.fullname }}</h4>
                <p class="text-secondary mb-1">{{ user.menurole }}</p>
                <p class="text-muted font-size-sm">{{ user.address }}</p>
                <p class="text-muted font-size-sm">{{ company.name }}</p>
              </div>
            </div>
          </div>
        </div>
        <div class="card mt-3">
          <ul class="list-group list-group-flush">
            <li class="
                  list-group-item
                  d-flex
                  justify-content-between
                  align-items-center
                  flex-wrap
                ">
              <h6 class="mb-0">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                  stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                  class="feather feather-globe mr-2 icon-inline">
                  <circle cx="12" cy="12" r="10"></circle>
                  <line x1="2" y1="12" x2="22" y2="12"></line>
                  <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z">
                  </path>
                </svg>Address
              </h6>
              <span class="text-secondary">{{ company.address }}</span>
            </li>
            <li class="
                  list-group-item
                  d-flex
                  justify-content-between
                  align-items-center
                  flex-wrap
                ">
              <h6 class="mb-0">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                  stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                  class="feather feather-github mr-2 icon-inline">
                  <path
                    d="M9 19c-5 1.5-5-2.5-7-3m14 6v-3.87a3.37 3.37 0 0 0-.94-2.61c3.14-.35 6.44-1.54 6.44-7A5.44 5.44 0 0 0 20 4.77 5.07 5.07 0 0 0 19.91 1S18.73.65 16 2.48a13.38 13.38 0 0 0-7 0C6.27.65 5.09 1 5.09 1A5.07 5.07 0 0 0 5 4.77a5.44 5.44 0 0 0-1.5 3.78c0 5.42 3.3 6.61 6.44 7A3.37 3.37 0 0 0 9 18.13V22">
                  </path>
                </svg>Mail
              </h6>
              <span class="text-secondary">{{ company.email }}</span>
            </li>
            <li class="
                  list-group-item
                  d-flex
                  justify-content-between
                  align-items-center
                  flex-wrap
                ">
              <h6 class="mb-0">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                  stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                  class="feather feather-twitter mr-2 icon-inline text-info">
                  <path
                    d="M23 3a10.9 10.9 0 0 1-3.14 1.53 4.48 4.48 0 0 0-7.86 3v1A10.66 10.66 0 0 1 3 4s-4 9 5 13a11.64 11.64 0 0 1-7 2c9 5 20 0 20-11.5a4.5 4.5 0 0 0-.08-.83A7.72 7.72 0 0 0 23 3z">
                  </path>
                </svg>Tel
              </h6>
              <span class="text-secondary">{{ company.tel }}</span>
            </li>
          </ul>
        </div>
      </div>
      <div class="col-md-8">
        <div class="card mb-3">
          <div class="card-body" style="padding-bottom:9px;">
            <div class="row">
              <div class="col-sm-3">
                <h6 class="mb-0">Full Name</h6>
              </div>
              <div class="col-sm-9 text-secondary">
                {{ user.full_name }}
              </div>
            </div>
            <hr />
            <div class="row">
              <div class="col-sm-3">
                <h6 class="mb-0">Email</h6>
              </div>
              <div class="col-sm-9 text-secondary">
                {{ user.email }}
              </div>
            </div>
            <hr />
            <div class="row">
              <div class="col-sm-3">
                <h6 class="mb-0">Personal Email</h6>
              </div>
              <div class="col-sm-9 text-secondary">
                {{ user.personal_email }}
              </div>
            </div>
            <hr />
            <div class="row">
              <div class="col-sm-3">
                <h6 class="mb-0">Mobile</h6>
              </div>
              <div class="col-sm-9 text-secondary"> {{ user.mobile }}</div>
            </div>
            <hr />
            <div class="row">
              <div class="col-sm-3">
                <h6 class="mb-0">Status</h6>
              </div>
              <div class="col-sm-9 text-secondary">
                {{ user.status }}
              </div>
            </div>
            <hr />
            <div class="row">
              <div class="col-sm-3">
                <h6 class="mb-0">Joinned From</h6>
              </div>
              <div class="col-sm-9 text-secondary">
                {{ user.created_at }}
              </div>
            </div>
            <br>
          </div>
        </div>
        <div class="card mb-3" style="margin-top:23px">
          <div class="card-body" style="margin-top:0px">
            <h6 class="d-flex align-items-center mb-3">
              <i class="material-icons text-info mr-2">{{ company.name }}</i>Lines & Divisions
            </h6>
            <hr />
            <div class="row" v-for="(detail, index) in details" :key="index">
              <div class="col-sm-3">
                <h6 class="mb-0">{{ detail.line.name }}</h6>
              </div>
              <div class="col-sm-6 text-secondary">
                {{ detail.name }}
              </div>
              <div class="col-sm-3">
                <c-button color="primary" class="btn-sm" @click="getLocation(detail)">
                  <CIcon class="text-white" name="marker" />
                </c-button>
              </div>
              <br>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- </div> -->
  </div>
</template>
<script>
export default {
  data() {
    return {
      user: null,
      url: null,
      details: [],
      company: [],
    };
  },
  methods: {
    initialize() {
      axios
        .get("/api/user-profile")
        .then((response) => {
          this.user = response.data.user;
          this.url = response.data.user.image;
          this.details = response.data.user_divisions;
          this.company = response.data.company;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getLocation(division) {
      let position = {
        lat: parseFloat(division.ll),
        lng: parseFloat(division.lg),
      };
      this.$mapDialog.open(`Map of Division id: ${division.name}`, [
        position,
      ]);
    }
  },
  created() {
    this.initialize();
  },
};
</script>
<style scoped>
body {
  margin-top: 5px;
  color: #1a202c;
  text-align: left;
  background-color: #e2e8f0;
}

.main-body {
  padding: 15px;
}

.card {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.card {
  position: relative;
  display: flex;
  flex-direction: column;
  min-width: 0;
  word-wrap: break-word;
  background-color: #fff;
  background-clip: border-box;
  border: 0 solid rgba(0, 0, 0, 0.125);
  border-radius: 0.25rem;
}

.card-body {
  flex: 1 1 auto;
  min-height: 1px;
  padding: 1rem;
}

.gutters-sm {
  margin-right: -8px;
  margin-left: -8px;
}

.gutters-sm>.col,
.gutters-sm>[class*="col-"] {
  padding-right: 8px;
  padding-left: 8px;
}

.mb-3,
.my-3 {
  margin-bottom: 1rem !important;
}

.bg-gray-300 {
  background-color: #e2e8f0;
}

.h-100 {
  height: 100% !important;
}

.shadow-none {
  box-shadow: none !important;
}
</style>
