<template>
  <c-col col="12" lg="12">
    <filter-data @getVisits="filter" />
    <c-card v-if="items.length != 0">
      <c-card-header>
        <h3 class="text-center">
          Out Of Location Report for Line: {{ items[0].line }} <br />
          from: {{ visitData.fromDate }} to: {{ visitData.toDate }}
        </h3>
      </c-card-header>
      <c-card-body>
        <c-data-table
          ref="content"
          add-table-classes="table-class"
          hover
          header
          striped
          sorter
          footer
          :items="items"
          :fields="actual_fields"
          :items-per-page="1000"
          :active-page="1"
          :responsive="true"
          pagination
          thead-top
          id="print"
        >
          <template slot="thead-top">
            <td style="border-top: none"><strong>Total</strong></td>
            <td style="border-top: none" class="text-xs-right">
              {{ items.length }}
            </td>
          </template>
          <!-- <template #visit_date="{ item }">
            <td>{{ format_date(item.visit_date) }}</td>
          </template> -->
          <template #division="{ item }">
            <td>
              <strong style="color: green">{{ item.division }}</strong>
            </td>
          </template>
          <template #account="{ item }">
            <td>
              <strong style="color: blue">{{ item.account }}</strong>
            </td>
          </template>
          <template #doctor="{ item }">
            <td>
              <strong style="color: blue">{{ item.doctor }}</strong>
            </td>
          </template>
          <template #out_distance="{ item }">
            <td>
              <strong style="color: red">{{ item.out_distance }}</strong>
            </td>
          </template>
          <template #map="{ item }">
            <td>
              <c-button
                v-if="checkPermission('show_single_actual_visits_locations')"
                color="primary"
                class="btn-sm mt-2 mr-1"
                @click="getLocation(item)"
                ><CIcon class="text-white" name="marker" />
              </c-button>
            </td>
          </template>
        </c-data-table>
      </c-card-body>
      <c-card-footer>
        <download
          @getPrint="print"
          @getxlsx="download"
          @getpdf="createPDF"
          @getcsv="downloadCsv"
          :fields="actual_fields"
          :data="items"
          :name="name"
        />
      </c-card-footer>
    </c-card>
  </c-col>
</template>

<script>
import download from "../../components/download-reports/download.vue";
import filterData from "../../components/reports/visits/filterData.vue";
import { Amiri } from "../../assets/fonts/Amiri-Regular-normal";
import jsPDF from "jspdf";
import "jspdf-autotable";
export default {
  components: {
    download,
    filterData,
  },
  data: () => {
    return {
      items: [],
      actual_fields: [
        "line",
        "division",
        "employee",
        "emp_code",
        "account",
        "brick",
        "doctor",
        "speciality",
        "acc_type",
        "type",
        "date",
        "map",
        "out_distance",
      ],
      visitData: {},
      name: "Out OF Location Report",
    };
  },
  emits: ["downloaded"],
  methods: {
    print() {
      this.$htmlToPaper("print");
    },
    download() {
      this.downloadXlsx(this.items, "Out OF Location Report.xlsx");
      this.$emit("downloaded");
    },
    downloadCsv() {
      this.downloadXlsx(this.items, "Out OF Location Report.csv");
      this.$emit("downloaded");
    },
    createPDF() {
      let pdfName = "OutOFLocation";
      const columns = [
        { title: "Line", dataKey: "line" },
        { title: "Division", dataKey: "division" },
        { title: "Employee", dataKey: "employee" },
        { title: "Account", dataKey: "account" },
        { title: "Brick", dataKey: "brick" },
        { title: "Doctor", dataKey: "doctor" },
        { title: "Account Type", dataKey: "acc_type" },
        { title: "Visit Type", dataKey: "type" },
        { title: "Comment", dataKey: "comment" },
        { title: "Visit Date", dataKey: "date" },
        { title: "Out Distance", dataKey: "out_distance" },
      ];
      const body = this.items;
      const doc = new jsPDF({ filters: ["ASCIIHexEncode"] });

      doc.addFileToVFS("Amiri-Regular.ttf", Amiri);
      doc.addFont("Amiri-Regular.ttf", "Amiri", "normal");
      doc.setFont("Amiri");
      doc.setFontSize(10);
      doc.autoTable({
        columns,
        body,
        margin: { top: 10 },
        showHead: "firstPage",
        styles: {
          lineColor: "#c7c7c7",
          lineWidth: 0,
          cellPadding: 2,
          font: "Amiri",
        },
      });
      doc.save(pdfName + ".pdf");
    },
    getLocation(visit) {
      axios
        .post("/api/visit-location", {
          visit,
        })
        .then((response) => {
          this.location = response.data.data;
          if (
            this.location.position.lat != 0 &&
            this.location.position.lng != 0
          ) {
            this.$mapDialog.open(`Map of Actual id: ${this.location.id}`, [
              this.location.position,
              this.location.positionAccount,
            ]);
          } else {
            this.flash("There is no loaction for this visit");
          }
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    filter({ visitFilter }) {
      this.visitData = visitFilter;
      axios
        .post("/api/out-of-location", {
          visitFilter,
        })
        .then((response) => {
          this.items = response.data.visits;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
  },
};
</script>
<style>
/* .table-class{
  position: relative;
} */

.table-class > thead > tr > th {
  background-color: #005cc8;
  color: white;
  text-align: center;
  position: sticky;
  top: 0;
}
</style>
