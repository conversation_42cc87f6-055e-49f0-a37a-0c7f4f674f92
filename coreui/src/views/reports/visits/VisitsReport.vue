<template>
  <c-col col="12" lg="12">
    <filter-data @getFilterVisits="filter"></filter-data>
    <c-card v-if="items.length !== 0">
      <c-card-header>
        <h3 class="text-center">
          Visits Report for Line: {{ lineName }} <br />
          from: {{ visitData.fromDate }} to: {{ visitData.toDate }}
        </h3>
      </c-card-header>
      <c-card-body>
        <v-container>
          <v-row style="justify-content: space-around" class="mt-2 px-16">
            <v-badge color="#1009EF" inline left> Account & Doctors </v-badge>
            <v-badge color="#198520" inline left> Division </v-badge>
            <v-badge color="#EFA609" inline left> AM </v-badge>
            <v-badge color="#EF09C2" inline left> PM </v-badge>
            <v-badge color="#09EFDE" inline left> Other </v-badge>
            <v-badge color="#f1c40f" inline left> Auto Double </v-badge>
            <v-badge color="#474787" inline left> Web </v-badge>
            <v-badge color="#b33939" inline left> App </v-badge>
          </v-row>
        </v-container>
        <c-data-table
          ref="content"
          add-table-classes="table-class"
          hover
          header
          striped
          sorter
          footer
          tableFilter
          :items="items"
          :fields="getFields()"
          :items-per-page="1000"
          :active-page="1"
          :responsive="true"
          pagination
          thead-top
          id="print"
        >
          <template slot="thead-top">
            <td style="border-top: none"><strong>Total</strong></td>
            <td style="border-top: none" class="text-xs-right">
              {{ items.length }}
            </td>
          </template>
          <template #comment="{ item }">
            <td @click="showMore(item.id)" v-if="!readMore[item.id]">
              {{ item.comment.substring(0, 30) + ".." }} <br /><span
                style="color: blue; cursor: pointer"
                >show more</span
              >
            </td>
            <td @click="showLess(item.id)" v-if="readMore[item.id]">
              {{ item.comment }} <br />
              <span style="color: blue; cursor: pointer">show less</span>
            </td>
          </template>
          <template #division="{ item }">
            <td>
              <strong style="color: green">{{ item.division }}</strong>
            </td>
          </template>
          <template #start="{ item }">
            <td>
              {{ format_date(item.start) }}
            </td>
          </template>
          <template #end="{ item }">
            <td>
              {{ format_date(item.end) }}
            </td>
          </template>
          <template #account="{ item }">
            <td>
              <strong style="color: blue; cursor: pointer"
                ><a @click="$root.$account('Account Data', item.account_id)">{{
                  item.account
                }}</a></strong
              >
            </td>
          </template>
          <template #doctor="{ item }">
            <td>
              <strong style="color: blue; cursor: pointer"
                ><a @click="$root.$doctor('Doctor Data', item.doctor_id)">{{
                  item.doctor
                }}</a></strong
              >
            </td>
          </template>
          <template #type="{ item }">
            <td>
              <strong
                v-if="item.is_automatic == 1"
                style="color: #f1c40f; cursor: pointer"
                >{{ item.type }}</strong
              >
              <strong v-else>{{ item.type }}</strong>
            </td>
          </template>
          <template #acc_type="{ item }">
            <td>
              <strong v-if="item.acc_shift_id == 1" style="color: #efa609">{{
                item.acc_type
              }}</strong>
              <strong v-if="item.acc_shift_id == 2" style="color: #ef09c2">{{
                item.acc_type
              }}</strong>
              <strong v-if="item.acc_shift_id == 3" style="color: #09efde">{{
                item.acc_type
              }}</strong>
            </td>
          </template>
          <template #visit_from="{ item }">
            <td>
              <strong v-if="item.is_web_visit == 1" style="color: #474787"
                >Web</strong
              >
              <strong v-if="item.is_web_visit == 0" style="color: #b33939"
                >App</strong
              >
            </td>
          </template>
          <template #status="{ item }">
            <td>
              <strong v-if="item.status == 1" style="color: green"
                >Approved</strong
              >
              <strong v-if="item.status == 0" style="color: red"
                >Dispproved</strong
              >
              <strong v-if="item.status == null" style="color: blue"
                >Pending</strong
              >
            </td>
          </template>
          <template #v_details="{ item, index }">
            <td class="py-2">
              <CButton
                color="primary"
                variant="outline"
                square
                size="sm"
                class="mt-3"
                @click="toggleDetails(item, index)"
              >
                {{ Boolean(item._toggled) ? "Hide" : "Details" }}
              </CButton>
            </td>
          </template>
          <template #details="{ item }">
            <CCollapse
              :show="Boolean(item._toggled)"
              :duration="collapseDuration"
            >
              <CCardBody>
                <c-data-table
                  hover
                  header
                  fixed-header
                  striped
                  sorter
                  :items="details"
                  :fields="details_fields"
                  :active-page="1"
                  :responsive="true"
                  pagination
                >
                </c-data-table>
              </CCardBody>
            </CCollapse>
          </template>
          <template #map="{ item }">
            <td>
              <c-button
                v-if="checkPermission('show_single_actual_visits_locations')"
                color="primary"
                class="btn-sm mt-2 mr-1"
                @click="getLocation(item)"
                ><CIcon class="text-white" name="marker" />
              </c-button>
            </td>
          </template>
          <template #plan_actions="{ item }">
            <td>
              <c-button
                color="success"
                class="btn-sm mt-2 mr-1"
                v-if="checkPermission('edit_plan_visits')"
                :to="{ name: 'EditPlanVisit', params: { id: item.id } }"
                ><CIcon name="cil-pencil"
              /></c-button>
              <c-button
                color="danger"
                class="btn-sm mt-2 mr-1"
                v-if="checkPermission('delete_plan_visits')"
                @click="
                  $dialog.open('Delete', 'Do you want to delete this record?', {
                      color: 'danger',
                    })
                    .then((confirmed) => {
                      if (confirmed) {
                        deletePlan(item);
                      }
                    })
                "
                ><c-icon name="cil-trash"
              /></c-button>
            </td>
          </template>
          <template #actual_actions="{ item }">
            <td>
              <CButton
                color="primary"
                class="btn-sm mt-2 mr-1"
                v-if="checkPermission('show_single_actual_visits')"
                :to="{ name: 'ActualVisit', params: { id: item.id } }"
                target="_blank"
                ><CIcon name="cil-magnifying-glass"
              /></CButton>
              <CButton
                color="success"
                class="btn-sm mt-2 mr-1"
                v-if="checkPermission('edit_actual_visits')"
                :to="{ name: 'EditActualVisit', params: { id: item.id } }"
                ><CIcon name="cil-pencil"
              /></CButton>
              <c-button
                color="danger"
                v-if="checkPermission('delete_actual_visits')"
                class="btn-sm mt-2 mr-1"
                @click="
                  $dialog.open('Delete', 'Do you want to delete this record?', {
                      color: 'danger',
                    })
                    .then((confirmed) => {
                      if (confirmed) {
                        deleteActualVisit(item);
                      }
                    })
                "
                ><c-icon name="cil-trash"
              /></c-button>
            </td>
          </template>
          <template #ow_actions="{ item }">
            <td>
              <CButton
                color="success"
                class="btn-sm mt-2 mr-1"
                v-if="checkPermission('edit_ow_actual_visits')"
                :to="{
                  name: 'EditOwActualVisit',
                  params: { id: item.id },
                }"
                ><c-icon name="cil-pencil"
              /></CButton>
              <c-button
                color="danger"
                class="btn-sm mt-2 mr-1"
                v-if="checkPermission('delete_ow_actual_visits')"
                @click="
                  $dialog.open('Delete', 'Do you want to delete this record?', {
                       color: 'danger',
                    })
                    .then((confirmed) => {
                      if (confirmed) {
                        deleteOwActual(item);
                      }
                    })
                "
                ><c-icon name="cil-trash"
              /></c-button>
            </td>
          </template>
        </c-data-table>
      </c-card-body>
      <c-card-footer>
        <download
          @getPrint="print"
          @getxlsx="download"
          @getpdf="createPDF"
          @getcsv="downloadCsv"
          :fields="plan_fields"
          :data="items"
          :name="name"
        />
      </c-card-footer>
    </c-card>
  </c-col>
</template>

<script>
import { mapState } from "vuex";
import moment from "moment";
import download from "../../../components/download-reports/download.vue";
import filterData from "../../../components/reports/visits/filterData.vue";
import ToolBarVisitsFilter from "./ToolBarVisitsFilter.vue";
import { Amiri } from "../../../assets/fonts/Amiri-Regular-normal";
import jsPDF from "jspdf";
import "jspdf-autotable";
import { capitalize } from "../../../filters";
export default {
  components: {
    download,
    filterData,
    ToolBarVisitsFilter,
  },
  data: () => {
    return {
      items: [],
      details: [],
      plan_fields: [],
      actual_fields: [],
      ow_fields: [],
      details_fields: [
        "product",
        "message",
        "v_feedback",
        "follow_up",
        "m_feedback",
        "samples",
        "giveaway",
        "units",
        "manager_feedback",
      ],
      collapseDuration: 0,
      visitData: {},
      readMore: {},
      location: null,
      lineName: null,
      name: "Visit Report",
    };
  },
  emits: ["downloaded"],
  computed: {
    ...mapState("company", ["companyName"]),
  },
  methods: {
    showMore(id) {
      this.$set(this.readMore, id, true);
    },
    showLess(id) {
      this.$set(this.readMore, id, false);
    },
    format_date: function (value) {
      if (value) {
        return moment(String(value)).format("YYYY-MM-DD HH:m:s");
      }
    },
    toggleDetails(item) {
      this.$set(this.items[item.num], "_toggled", !item._toggled);
      this.collapseDuration = 300;
      this.$nextTick(() => {
        this.collapseDuration = 0;
      });
      axios
        .post("/api/visit-details", {
          item,
          visitFilter: this.visitData,
        })
        .then((response) => {
          this.details = response.data.data;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getFields() {
      if (this.visitData.visitType == 1) return this.plan_fields;
      else if (this.visitData.visitType == 2) return this.actual_fields;
      else return this.ow_fields;
    },
    print() {
      this.$htmlToPaper("print");
    },
    getLocation(visit) {
      axios
        .post("/api/visit-location", {
          visit,
        })
        .then((response) => {
          this.location = response.data.data;
          if (
            this.location.position.lat != 0 &&
            this.location.position.lng != 0
          ) {
            this.$mapDialog.open(`Map of Actual id: ${this.location.id}`, [
              this.location.position,
              this.location.positionAccount,
            ]);
          } else {
            this.flash("There is no loaction for this visit");
          }
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    download() {
      this.downloadXlsx(this.items, "Visits Report.xlsx");
      this.$emit("downloaded");
    },
    downloadCsv() {
      this.downloadXlsx(this.items, "Visits Report.csv");
      this.$emit("downloaded");
    },
    createPDF() {
      let pdfName = this.name;
      const columns = this.actual_fields.map((field) => {
        return {
          title: capitalize(field),
          dataKey: field,
        };
      });
      const body = this.items;
      const doc = new jsPDF({ filters: ["ASCIIHexEncode"] });

      doc.addFileToVFS("Amiri-Regular.ttf", Amiri);
      doc.addFont("Amiri-Regular.ttf", "Amiri", "normal");
      doc.setFont("Amiri");
      doc.setFontSize(10);
      doc.autoTable({
        columns,
        body,
        margin: { top: 10 },
        showHead: "firstPage",
        styles: {
          lineColor: "#c7c7c7",
          lineWidth: 0,
          cellPadding: 2,
          font: "Amiri",
        },
      });
      doc.save(pdfName + ".pdf");
    },
    filter(visitFilter) {
      console.log(visitFilter);
      this.visitData = visitFilter;
      axios
        .post("/api/filterReport", {
          visitFilter,
        })
        .then((response) => {
          this.items = response.data.visits.map((item, num) => {
            return { ...item, num };
          });
          this.actual_fields = response.data.actual_fields;
          this.plan_fields = response.data.plan_fields;
          this.ow_fields = response.data.ow_fields;
          this.getFields();
          this.lineName = response.data.line;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    removePlan(item) {
      const index = this.items.findIndex((plan) => item.id == plan.id);
      this.items.splice(index, 1);
    },
    deletePlan(item) {
      axios
        .delete(`/api/plans-daily/${item.id}`)
        .then((res) => {
          this.removePlan(item);
          this.flash("Plan Deleted Successfully");
        })
        .catch((err) => this.showErrorMessage(err));
    },
    removeActualVisit(item) {
      const index = this.items.findIndex((type) => item.id == type.id);
      this.items.splice(index, 1);
    },
    deleteActualVisit(item) {
      axios
        .delete(`/api/actual_visits/${item.id}`)
        .then((res) => {
          this.removeActualVisit(item);
          this.flash("Actual Visit Deleted Successfully");
        })
        .catch((err) => this.showErrorMessage(err));
    },
    removeOwActual(item) {
      const index = this.items.findIndex((type) => item.id == type.id);
      this.items.splice(index, 1);
    },
    deleteOwActual(item) {
      axios
        .delete(`/api/owactualvisits/${item.id}`)
        .then((res) => {
          this.removeOwActual(item);
          this.flash("OW Deleted Successfully");
        })
        .catch((err) => this.showErrorMessage(err));
    },
  },
};
</script>
<style>
/* .table-class{
  position: relative;
} */

.table-class > thead > tr > th {
  background-color: #005cc8;
  color: white;
  text-align: center;
  position: sticky;
  top: 0;
}
</style>
