<template>
  <c-col col="12" lg="12">
    <filter-data @getSchedule="filter"/>
    <c-card v-if="items.length != 0">
      <c-card-header>
        <h3 class="text-center">
          Working Hours Report for Line: {{ items[0].line }} <br/>
          from: {{ workingData.fromDate }} to: {{ workingData.toDate }}
        </h3>
      </c-card-header>
      <c-card-body>
        <c-data-table
          ref="content"
          hover
          header
          tableFilter
          striped
          sorter
          footer
          :items="items"
          :fields="fields"
          :items-per-page="1000"
          :active-page="1"
          :responsive="true"
          pagination
          thead-top
          id="print"
        >
          <template slot="thead-top">
            <td style="border-top: none"><strong>Total</strong></td>
            <td style="border-top: none" class="text-xs-right">
              {{ items.length }}
            </td>
          </template>
          <template v-for="field in clickable_fields" v-slot:[field]="{ item }">
            <td :key="field">
              <h6
                @click="rowClickHandler(item, field)"
                v-bind:style="{ cursor: 'pointer' }"
              >
                {{ item[field] }}
              </h6>
            </td>
          </template>
          <template #division="{ item }">
            <td>
              <strong
                :style="{
                  color: item.color,
                }"
              >{{ item.division }}</strong
              >
            </td>
          </template>
          <template #employee="{ item }">
            <td>
              {{ item.employee }}
            </td>
          </template>
          <template #checkin_map="{ item }">
            <td>
              <c-button v-if="checkPermission('show_single_actual_visits_locations')" color="primary"
                        class="btn-sm mt-2 mr-1" @click="getLocation(item,)">
                <CIcon class="text-white" name="marker"/>
              </c-button>
            </td>
          </template>
          <template #checkout_map="{ item }">
            <td>
              <c-button v-if="checkPermission('show_single_actual_visits_locations')" color="primary"
                        class="btn-sm mt-2 mr-1" @click="getLocation(item,false)">
                <CIcon class="text-white" name="marker"/>
              </c-button>
            </td>
          </template>
        </c-data-table>
      </c-card-body>
      <c-card-footer>
        <download
          @getPrint="print"
          @getxlsx="download"
          @getpdf="createPDF"
          @getcsv="downloadCsv"
          :fields="fields"
          :data="items"
          :name="name"
        />
      </c-card-footer>
    </c-card>
  </c-col>
</template>

<script>
import download from "../../components/download-reports/download.vue";
import filterData from "../../components/reports/workingHours/filterData.vue";
import {Amiri} from "../../assets/fonts/Amiri-Regular-normal";
import jsPDF from "jspdf";
import "jspdf-autotable";

export default {
  components: {
    download,
    filterData,
  },
  data: () => {
    return {
      clickable_fields: [],
      items: [],
      fields: [],
      workingData: {},
      lineName: null,
      name: "Working Hours",
    };
  },
  emits: ["downloaded"],
  methods: {
    rowClickHandler(item, field) {
      axios
        .post(`/api/show-visit-statistics`, {
          listFilter: this.workingData,
          div: this.workingData.filter == 1 ? item.id : null,
          user: this.workingData.filter == 2 ? item.id : null,
          column: field,
        })
        .then((response) => {
          const data = response.data.data;
          this.$root.$table("Working Hours", data);
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    print() {
      this.$htmlToPaper("print");
    },
    download() {
      let filteredData = Object.values(this.items);
      filteredData.forEach((element) => {
        delete element["color"];
      });

      this.downloadXlsx(filteredData, "Working Hours.xlsx");
      this.$emit("downloaded");
    },
    downloadCsv() {
      let filteredData = Object.values(this.items);
      filteredData.forEach((element) => {
        delete element["color"];
      });

      this.downloadXlsx(filteredData, "Working Hours.csv");
      this.$emit("downloaded");
    },
    createPDF() {
      let pdfName = "Working Hours";
      const columns = [
        {title: "Line", dataKey: "line"},
        {title: "Division", dataKey: "division"},
        {title: "Employee", dataKey: "employee"},
        {title: "Plans", dataKey: "plans"},
        {title: "Actual From Plan", dataKey: "actual_from_plan"},
        {title: "Achievement", dataKey: "achievement"},
        {title: "Direct Actual", dataKey: "direct_actual"},
        {title: "AM", dataKey: "am"},
        {title: "PM", dataKey: "pm"},
        {title: "Total Actual", dataKey: "total_actual"},
      ];
      const body = this.items;
      const doc = new jsPDF({filters: ["ASCIIHexEncode"]});

      doc.addFileToVFS("Amiri-Regular.ttf", Amiri);
      doc.addFont("Amiri-Regular.ttf", "Amiri", "normal");
      doc.setFont("Amiri");
      doc.setFontSize(10);
      doc.autoTable({
        columns,
        body,
        margin: {top: 10},
        showHead: "firstPage",
        styles: {
          lineColor: "#c7c7c7",
          lineWidth: 0,
          cellPadding: 2,
          font: "Amiri",
        },
      });
      doc.save(pdfName + ".pdf");
    },
    filter({workingFilter}) {
      this.workingData = workingFilter;
      axios
        .post(`/api/working-hours`, {
          workingFilter,
        })
        .then((response) => {
          this.items = response.data.data;
          this.fields = response.data.fields;
          this.clickable_fields = response.data.clickable_fields;
          this.lineName = response.data.line;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getLocation(item, isCheckIn = true) {
      const head = isCheckIn ? 'CheckIn' : 'CheckOut';
      const title = `Map of ${head}`;
      const lat = isCheckIn ? item.checkin_ll : item.checkin_ll;
      const lng = isCheckIn ? item.checkin_lg : item.checkin_lg;
      if (
        lat !== 0 &&
        lng !== 0
      ) {
        this.$mapDialog.open(title, [
          {
            lat,
            lng
          }
        ]);
      } else {
        this.flash("There is no loaction for this visit");
      }
    }
  },
};
</script>
