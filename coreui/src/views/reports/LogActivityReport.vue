<template>
  <CRow>
    <CCol col="12" lg="12">
      <CCard>
        <c-card-header>Log Activity Report</c-card-header>
        <CCardBody>
          <div class="row">
            <div class="col-lg-3 col-md-3 col-sm-8">
              <c-form-group>
                <template #label>
                  <strong>Type</strong>
                </template>
                <template #input>
                  <v-select v-model="type" :options="['Log Activity', 'App Log']" placeholder="Select Type"
                    class="mt-2" />
                </template>
              </c-form-group>
            </div>
            <div class="col-lg-3 col-md-3 col-sm-8">
              <c-form-group>
                <template #label>
                  <strong>Employee</strong>
                </template>
                <template #input>
                  <input label="All" v-if="users.length != 0" id="user" class="m-1" type="checkbox"
                    v-model="checkAllUsers" title="Check All Users" @change="checkAllEmployees" />
                  <label for="user" v-if="users.length != 0" style="font-weight: bold">All</label>
                  <v-select v-model="user_id" :options="users" :reduce="(user) => user.id" label="fullname" :value="0"
                    placeholder="Select Employee" class="mt-2" multiple />
                </template>
              </c-form-group>
            </div>
            <div class="col-lg-3 col-md-3 col-sm-8" v-if="type == 'Log Activity'">
              <c-form-group>
                <template #label>
                  <strong>Actions</strong>
                </template>
                <template #input>
                  <input label="All" v-if="actions.length != 0" id="action" class="m-1" type="checkbox"
                    v-model="checkAllActions" title="Check All Actions" @change="checkAllAction" />
                  <label for="action" v-if="actions.length != 0" style="font-weight: bold">All</label>
                  <v-select v-model="action_id" :options="actions" :reduce="(action) => action.id" label="name"
                    :value="0" placeholder="Select Action" class="mt-2" multiple />
                </template>
              </c-form-group>
            </div>
            <div class="col-lg-3 col-md-3 col-sm-8">
              <CInput label="From Date" type="date" placeholder="Date" v-model="fromDate" :formatter="format"></CInput>
            </div>
            <div class="col-lg-3 col-md-3 col-sm-8">
              <CInput label="To Date" type="date" placeholder="Date" v-model="toDate" :formatter="format"></CInput>
            </div>
          </div>
        </CCardBody>
        <CCardFooter>
          <CButton class="text-white" color="primary" @click="filter()">Filter</CButton>
        </CCardFooter>
      </CCard>
      <CCard v-if="logactivities.length != 0">
        <CCardBody>
          <CDataTable id="print" ref="printTable" hover striped sorter footer tableFilter :items="logactivities"
            :fields="logactivities_fields" :items-per-page="1000" :active-page="1" :responsive="true" pagination
            item-key="serial">
            <template #launch_date="{ item }">
              <td>{{ format_date(item.launch_date) }}</td>
            </template>
            <template slot="thead-top">
              <td style="border-top: none"><strong>Total</strong></td>
              <td style="border-top: none" class="text-xs-right">
                {{ logactivities.length }}
              </td>
              <td colspan="3" style="border-top: none">
                <h3 class="text-center">Log Activities Report</h3>
                <br />
                <h3 class="text-center">{{ fromDate }} to {{ toDate }}</h3>
                <br />
              </td>
            </template>
            <template #actions="{ item }">
              <td>
                <div class="row justify-content-center">
                  <c-button v-if="item.position.lat != null && item.position.lng != null
                  " color="primary" class="btn-sm mt-2 mr-1" @click="
                    $mapDialog.open(`Map of log Activity id: ${item.id}`, [
                      item.position,
                    ])
                    ">
                    <CIcon class="text-white" name="marker" />
                  </c-button>

                  <!-- Restore -->

                  <c-button v-if="item.action_id == 5 &&
                    !isHidden &&
                    item.has_soft_delete &&
                    !item.is_exist
                  " color="success" class="btn-sm mt-2 mr-1 text-white" @click="
$dialog.open(
                        'Restore',
                        'Do you want to Restore this record?',
                        {
                           color: 'success',
                        }
                      )
                      .then((confirmed) => {
                        if (confirmed) {
                          restore(item.id);
                        }
                      })
                    ">
                    <CIcon name="cilActionUndo" />
                  </c-button>

                  <!-- delete forever -->
                  <c-button v-if="item.action_id == 5 &&
                    !isHidden &&
                    item.has_soft_delete &&
                    !item.is_exist
                  " color="danger" class="btn-sm mt-2 mr-1" @click="
$dialog.open(
                        'Restore',
                        'Do you want to Delete this record forever?',
                        {
                           color: 'danger',
                        }
                      )
                      .then((confirmed) => {
                        if (confirmed) {
                          forceDelete(item.id);
                        }
                      })
                    ">
                    <CIcon name="cil-grid-slash" />
                  </c-button>
                </div>
              </td>
            </template>
          </CDataTable>
        </CCardBody>
        <c-card-footer>
          <download @getPrint="print" @getxlsx="download" @getpdf="createPDF" @getcsv="downloadCsv"
            :fields="logactivities_fields" :data="logactivities" :name="name" />
        </c-card-footer>
      </CCard>
    </CCol>
  </CRow>
</template>

<script>
import moment from "moment";
import download from "./../../components/download-reports/download.vue";
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
import jsPDF from "jspdf";
import "jspdf-autotable";
const today = new Date();
export default {
  components: {
    vSelect,
    download,
  },
  emits: ["downloaded"],

  data() {
    return {
      users: [],
      user_id: [],
      type: 'Log Activity',
      fromDate: moment(today).format("YYYY-MM-DD"),
      toDate: moment(today).format("YYYY-MM-DD"),
      logactivities: [],
      actions: [
        { id: 1, name: 'Show All', 'key': 'show_all' },
        { id: 2, name: 'Create', 'key': 'create' },
        { id: 3, name: 'Show Single', 'key': 'show_single' },
        { id: 4, name: 'Edit', 'key': 'edit' },
        { id: 5, name: 'Delete', 'key': 'delete' },
        { id: 6, name: 'Restore', 'key': 'restore' },
        { id: 10, name: 'Import', 'key': 'import' },
        { id: 24, name: 'Reset', 'key': 'reset' },
      ],
      action_id: [],
      logactivities_fields: [],
      name: "Activity Log Report",
      checkAllUsers: false,
      checkAllActions: false,
      isHidden: false,
    };
  },
  methods: {
    initialize() {
      axios
        .get("/api/get-data-activity")
        .then((response) => {
          this.users = response.data.users;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    checkAllEmployees() {
      if (this.checkAllUsers) this.user_id = this.users.map((item) => item.id);
      if (this.checkAllUsers == false) this.user_id = null;
    },
    checkAllAction() {
      if (this.checkAllActions) this.action_id = this.actions.map((item) => item.id);
      if (this.checkAllActions == false) this.action_id = null;
    },
    format(value, event) {
      return moment(value).format("YYYY-MM-DD");
    },
    format_date: function (value) {
      let date_format = localStorage.getItem("date_format");
      if (value) {
        return moment(String(value)).format(date_format);
      }
    },
    download() {
      this.downloadXlsx(this.logactivities, "Log Activity Report.xlsx");
      this.$emit("downloaded");
    },
    downloadCsv() {
      this.downloadXlsx(this.logactivities, "Log Activity Report.csv");
      this.$emit("downloaded");
    },
    print() {
      this.$htmlToPaper("print");
    },
    createPDF() {
      let pdfName = "LogActivityReport";
      var columns = [
        { title: "ID", dataKey: "id" },
        { title: "IP", dataKey: "ip" },
        { title: "User Name", dataKey: "user_name" },
        { title: "Full Name", dataKey: "full_name" },
        { title: "Permission Name", dataKey: "permission_name" },
        { title: "Activity Date", dataKey: "activity date" },
      ];
      var doc = new jsPDF();
      doc.autoTable(columns, this.logactivities, {
        margin: { top: 10 },
        showHead: "firstPage",
      });
      doc.save(pdfName + ".pdf");
    },
    filter() {
      axios
        .post("/api/logActivityReport", {
          users: this.user_id,
          actions: this.action_id,
          type: this.type,
          from_date: this.fromDate,
          to_date: this.toDate,
        })
        .then((response) => {
          this.logactivities = response.data.logactivities;
          this.logactivities_fields = response.data.logactivities_fields;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    restore(id) {
      axios
        .post(`/api/restore/${id}`)
        .then((response) => {
          this.isHidden = true;
          this.flash("Data Successfully Restored.");
          this.filter();
          this.isHidden = false;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    forceDelete(id) {
      axios
        .post(`/api/force-delete/${id}`)
        .then((response) => {
          this.isHidden = true;
          this.flash("Data Successfully Deleted Forever.");
          this.filter();
          this.isHidden = false;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
  },
  created() {
    this.initialize();
  },
};
</script>
