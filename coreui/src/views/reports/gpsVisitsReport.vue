<template>
  <c-col col="12" lg="12">
    <filter-data @getSchedule="filter" />
    <c-card v-if="items.length != 0">
      <c-card-header>
        <h3 class="text-center">
          Overall Visits Report for Line: {{ lineName }} <br />
          from: {{ visitData.fromDate }} to: {{ visitData.toDate }}
        </h3>
      </c-card-header>
      <c-card-body>
        <v-container>
          <v-row style="justify-content: space-around" class="mt-2 px-16">
            <v-badge color="#2EB85C" inline left> Near </v-badge>
            <v-badge color="#F9B115" inline left> First Visit </v-badge>
            <v-badge color="#E55353" inline left> Far </v-badge>
            <v-badge color="#321FDB" inline left> No Location </v-badge>
          </v-row>
        </v-container>
        <c-data-table ref="content" add-table-classes="table-class" hover header striped sorter footer tableFilter
          :items="items" :fields="getFields()" :items-per-page="1000" :active-page="1" :responsive="true" pagination
          thead-top id="print">
          <template slot="thead-top">
            <td style="border-top: none"><strong>Total</strong></td>
            <td style="border-top: none" class="text-xs-right">
              {{ items.length }}
            </td>
          </template>
          <template #comment="{ item }">
            <td @click="showMore(item.id)" v-if="!readMore[item.id]">
              {{ item.comment.substring(0, 30) + ".." }} <br /><span style="color: blue; cursor: pointer">show
                more</span>
            </td>
            <td @click="showLess(item.id)" v-if="readMore[item.id]">
              {{ item.comment }} <br />
              <span style="color: blue; cursor: pointer">show less</span>
            </td>
          </template>
          <template #division="{ item }">
            <td>
              <strong style="color: green">{{ item.division }}</strong>
            </td>
          </template>
          <template #start="{ item }">
            <td>
              {{ format_date(item.start) }}
            </td>
          </template>
          <template #end="{ item }">
            <td>
              {{ format_date(item.end) }}
            </td>
          </template>




          <template #Status="{ item }">
            <td>
              <!-- <v-chip v-if="item.Status == 'Far'" :style="{ backgroundColor: '#E55353' , color: 'white'}">
                <strong>{{ item.Status }}</strong>
              </v-chip> -->
              <CBadge color="danger" v-if="item.Status == 'Far'"> <strong>{{ item.Status }}</strong> </CBadge>
              <CBadge color="success" v-if="item.Status == 'Near'"> <strong>{{ item.Status }}</strong> </CBadge>
              <CBadge color="warning" v-if="item.Status == 'First Visit'"> <strong>{{ item.Status }}</strong> </CBadge>
              <CBadge color="primary" v-if="item.Status == 'No Location'"> <strong>{{ item.Status }}</strong> </CBadge>
              <!-- <v-chip v-if="item.Status == 'Near'" :style="{ backgroundColor: '#2EB85C', color: 'white' }">
                <strong>{{ item.Status }}</strong>
              </v-chip>
              <v-chip v-if="item.Status == 'First Visit'" :style="{ backgroundColor: '#F9B115' , color: 'white'}">
                <strong>{{ item.Status }}</strong>
              </v-chip>
              <v-chip v-if="item.Status == 'No Location'" :style="{ backgroundColor: '#5141E0' , color: 'white'}">
                <strong>{{ item.Status }}</strong>
              </v-chip> -->
            </td>
          </template>

          <template #under_time="{ item }">
            <td v-if="item.under_time == 'Under Time'">
              <CBadge color="danger"> <strong>{{ item.under_time }}</strong> </CBadge>
            </td>
            <td v-if="item.under_time == 'No Duration'">
              <CBadge color="warning"> <strong style="color: white;">{{ item.under_time }}</strong> </CBadge>
            </td>
            <td v-if="item.under_time == 'Valid Duration'">
              <CBadge color="success"> <strong>{{ item.under_time }}</strong> </CBadge>
            </td>
          </template>


          <template #Map="{ item }">
            <td>
              <c-button v-if="checkPermission('show_single_actual_visits_locations') && item.status != 'No Location'"
                color="primary" class="btn-sm mt-2 mr-1" @click="getLocation(item)">
                <CIcon class="text-white" name="marker" />
              </c-button>
            </td>
          </template>

        </c-data-table>
      </c-card-body>
      <c-card-footer>
        <download @getPrint="print" @getxlsx="download" @getpdf="createPDF" @getcsv="downloadCsv"
          :fields="actual_fields" :data="items" :name="name" />
      </c-card-footer>
    </c-card>
  </c-col>
</template>

<script>
import { mapState } from "vuex";
import moment from "moment";
import download from "../../components/download-reports/download.vue";
import filterData from "../../components/reports/gpsVisits/filterData.vue";
import { Amiri } from "../../assets/fonts/Amiri-Regular-normal";
import jsPDF from "jspdf";
import "jspdf-autotable";
import { capitalize } from "../../filters";
export default {
  components: {
    download,
    filterData,
    capitalize,
  },
  data: () => {
    return {
      items: [],
      details: [],
      actual_fields: [],
      details_fields: [
        "product",
        "message",
        "v_feedback",
        "follow_up",
        "m_feedback",
        "samples",
        "giveaway",
        "units",
        "manager_feedback",
      ],
      details: [],
      collapseDuration: 0,
      visitData: {},
      readMore: {},
      location: null,
      lineName: null,
      name: "GPS Visit Report",
    };
  },
  emits: ["downloaded"],
  computed: {
    ...mapState("company", ["companyName"]),
  },
  methods: {
    showMore(id) {
      this.$set(this.readMore, id, true);
    },
    showLess(id) {
      this.$set(this.readMore, id, false);
    },
    format_date: function (value) {
      if (value) {
        return moment(String(value)).format("YYYY-MM-DD HH:m:s");
      }
    },
    toggleDetails(item) {
      this.$set(this.items[item.num], "_toggled", !item._toggled);
      this.collapseDuration = 300;
      this.$nextTick(() => {
        this.collapseDuration = 0;
      });
      axios
        .post("/api/visit-details", {
          item,
          visitFilter: this.visitData,
        })
        .then((response) => {
          this.details = response.data.data;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getFields() {
      return this.actual_fields;
    },
    print() {
      this.$htmlToPaper("print");
    },
    getLocation(visit) {
      axios
        .post("/api/visit-location", {
          visit,
        })
        .then((response) => {
          this.location = response.data.data;
          if (
            this.location.position.lat != 0 &&
            this.location.position.lng != 0
          ) {
            this.$mapDialog.open(`Map of Actual id: ${this.location.id}`, [
              this.location.position,
              this.location.positionAccount,
            ]);
          } else {
            this.flash("There is no loaction for this visit");
          }
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    download() {
      this.downloadXlsx(this.items, "Visits Report.xlsx");
      this.$emit("downloaded");
    },
    downloadCsv() {
      this.downloadXlsx(this.items, "Visits Report.csv");
      this.$emit("downloaded");
    },
    createPDF() {
      let pdfName = this.name;
      const columns = this.actual_fields.map((field) => {
        return {
          title: capitalize(field),
          dataKey: field,
        };
      });
      const body = this.items;
      const doc = new jsPDF({ filters: ["ASCIIHexEncode"] });

      doc.addFileToVFS("Amiri-Regular.ttf", Amiri);
      doc.addFont("Amiri-Regular.ttf", "Amiri", "normal");
      doc.setFont("Amiri");
      doc.setFontSize(10);
      doc.autoTable({
        columns,
        body,
        margin: { top: 10 },
        showHead: "firstPage",
        styles: {
          lineColor: "#c7c7c7",
          lineWidth: 0,
          cellPadding: 2,
          font: "Amiri",
        },
      });
      doc.save(pdfName + ".pdf");
    },
    filter({ visitFilter }) {
      console.log(this.companyName);
      this.visitData = visitFilter;
      axios
        .post("/api/gps-visits", {
          visitFilter,
        })
        .then((response) => {
          this.items = response.data.visits.map((item, num) => {
            return { ...item, num };
          });
          this.actual_fields = response.data.actual_fields;
          this.getFields();
          this.lineName = response.data.line;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    removePlan(item) {
      const index = this.items.findIndex((plan) => item.id == plan.id);
      this.items.splice(index, 1);
    },
    deletePlan(item) {
      axios
        .delete(`/api/plans-daily/${item.id}`)
        .then((res) => {
          this.removePlan(item);
          this.flash("Plan Deleted Successfully");
        })
        .catch((err) => this.showErrorMessage(err));
    },
    removeActualVisit(item) {
      const index = this.items.findIndex((type) => item.id == type.id);
      this.items.splice(index, 1);
    },
    deleteActualVisit(item) {
      axios
        .delete(`/api/actual_visits/${item.id}`)
        .then((res) => {
          this.removeActualVisit(item);
          this.flash("Actual Visit Deleted Successfully");
        })
        .catch((err) => this.showErrorMessage(err));
    },
    removeOwActual(item) {
      const index = this.items.findIndex((type) => item.id == type.id);
      this.items.splice(index, 1);
    },
    deleteOwActual(item) {
      axios
        .delete(`/api/owactualvisits/${item.id}`)
        .then((res) => {
          this.removeOwActual(item);
          this.flash("OW Deleted Successfully");
        })
        .catch((err) => this.showErrorMessage(err));
    },
  },
};
</script>
<style>
/* .table-class{
  position: relative;
} */

.table-class>thead>tr>th {
  background-color: #005cc8;
  color: white;
  text-align: center;
  position: sticky;
  top: 0;
}
</style>
