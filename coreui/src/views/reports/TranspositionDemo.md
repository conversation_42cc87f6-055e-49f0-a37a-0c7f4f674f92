# Data Table Transposition Feature

## Overview
The SaleAchievementComparisonReport.vue now includes automatic data transposition when there are more than 10 header columns, making wide tables more readable and manageable.

## How It Works

### Automatic Transposition
- **Trigger**: Automatically activates when `fields.length > 10`
- **Manual Override**: Users can toggle between normal and transposed views using the toggle button
- **Visual Indicator**: Badge shows current view mode and column count

### Data Transformation

#### Original Data Structure
```javascript
// Original fields
fields: ['name', 'jan_2024', 'feb_2024', 'mar_2024', ..., 'dec_2024'] // 13 columns

// Original items
items: [
  { name: 'Product A', jan_2024: 100, feb_2024: 120, mar_2024: 110, ... },
  { name: 'Product B', jan_2024: 80, feb_2024: 90, mar_2024: 95, ... },
  { name: 'Product C', jan_2024: 150, feb_2024: 140, mar_2024: 160, ... }
]
```

#### Transposed Data Structure
```javascript
// Transposed fields
transposedFields: ['field_name', 'row_0', 'row_1', 'row_2']

// Transposed items
transposedItems: [
  { field_name: 'Name', row_0: 'Product A', row_1: 'Product B', row_2: 'Product C' },
  { field_name: 'Jan 2024', row_0: 100, row_1: 80, row_2: 150 },
  { field_name: 'Feb 2024', row_0: 120, row_1: 90, row_2: 140 },
  { field_name: 'Mar 2024', row_0: 110, row_1: 95, row_2: 160 },
  // ... more rows for each month
]

// Transposed headers
transposedHeaders: ['Field', 'Product A', 'Product B', 'Product C']
```

## Features

### 1. Automatic Detection
- Monitors `fields.length` to determine when transposition should be applied
- Threshold set to 10 columns (configurable)

### 2. Manual Toggle
- Toggle button allows users to switch between views
- Button shows appropriate icon and text for current state
- State resets to auto-detect when new data is loaded

### 3. Dynamic Headers
- Extracts meaningful row identifiers (name, code, id) for column headers
- Falls back to "Row X" format if no identifier found
- Headers are displayed in a custom thead-top template

### 4. Color Preservation
- Maintains `rank_color` styling from original data
- Colors are preserved per cell in transposed view
- Uses `getCellColor()` method to handle both normal and transposed views

### 5. Export Compatibility
- All export functions (Excel, CSV, PDF) work with transposed data
- File names include "_transposed" suffix when in transposed mode
- PDF column titles are properly mapped for transposed view

## Implementation Details

### Key Computed Properties
- `shouldTranspose`: Determines if transposition should be active
- `displayFields`: Returns appropriate fields array (normal or transposed)
- `displayItems`: Returns appropriate items array (normal or transposed)
- `transposedFields`: Generates field names for transposed view
- `transposedItems`: Performs the actual data transposition
- `transposedHeaders`: Creates meaningful column headers

### Key Methods
- `formatFieldName()`: Converts field names to readable format
- `getCellColor()`: Handles color styling for both views
- `getContrastColor()`: Ensures text readability on colored backgrounds

## Usage Example

```vue
<template>
  <!-- The component automatically handles transposition -->
  <c-data-table 
    :items="displayItems" 
    :fields="displayFields"
    :responsive="true">
    
    <!-- Custom headers for transposed view -->
    <template v-if="shouldTranspose" slot="thead-top">
      <th v-for="(header, index) in transposedHeaders" :key="index">
        {{ header }}
      </th>
    </template>
    
    <!-- Dynamic cell rendering with color preservation -->
    <template v-for="field in displayFields" #[field]="{ item }">
      <td :style="{ 
        backgroundColor: getCellColor(item, field), 
        color: getCellColor(item, field) !== 'transparent' ? 
               getContrastColor(getCellColor(item, field)) : 'inherit'
      }">
        {{ item[field] }}
      </td>
    </template>
  </c-data-table>
</template>
```

## Benefits

1. **Improved Readability**: Wide tables become vertically oriented for better scanning
2. **Better Mobile Experience**: Fewer horizontal columns to scroll through
3. **Preserved Functionality**: All existing features (sorting, filtering, export) continue to work
4. **User Control**: Manual toggle allows users to choose their preferred view
5. **Automatic Optimization**: No manual intervention required for wide datasets
6. **Visual Feedback**: Clear indicators show current view mode and reasoning

## Configuration

The transposition threshold can be adjusted by modifying the condition in `shouldTranspose`:

```javascript
shouldTranspose() {
  if (this.forceTranspose !== null) {
    return this.forceTranspose;
  }
  return this.fields.length > 10; // Change this threshold as needed
}
```
