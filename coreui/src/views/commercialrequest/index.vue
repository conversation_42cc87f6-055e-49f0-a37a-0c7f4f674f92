<template>
  <c-row>
    <c-col col="12" xl="12">
      <transition name="slide">
        <c-card>
          <c-card-header>Commercial and Branding
            <c-button style="background-color: blueviolet; color: white; float: right" class="btn-sm mt-2 mr-1"
              :items="items" :to="{ name: 'PrintCommercials' }">
              <c-icon name="cil-print" />
            </c-button>
          </c-card-header>
          <c-card-body>
            <c-button v-if="checkPermission('create_commercial_requests')" color="primary"
              :to="{ name: 'CreateCommercial' }">Create Commercial Request
            </c-button>
            <CDropdown placement="bottom-end" toggler-text="Tools" color="primary"
              class="float-right text-white text-white d-inline-block">
              <CDropdownItem v-if="checkPermission('download_all_templates')"
                @click="templateDownload('commercialDoctors_Template.xlsx')">Template</CDropdownItem>

              <c-dropdown-item v-if="checkPermission('import_classes')" @click="successModal = true">Upload
                Doctors</c-dropdown-item>
            </CDropdown>

            <CModal title="Upload Classes" color="success" :show.sync="successModal">
              <CInputFile type="file" ref="file" id="file" name="file_name" v-on:change="handleFileUpload"
                placeholder="New file" />
              <CProgress :value="uploadPercentage" color="success" animated showPercentage show-value
                style="height: 15px" class="mt-1" :max="100" v-show="progressBar" />
              <template #footer>
                <CButton class="text-white" @click="successModal = false" color="danger">Discard</CButton>
                <CButton class="text-white" @click="importDoctors()" color="success">Upload</CButton>
              </template>
            </CModal>
            <div>
              <br />
              <c-button style="float: right;background-color: blueviolet;" class="text-white" @click="getData()">
                <CIcon name="cil-reload" />
              </c-button>
            </div>
            <br /><br /> 
            <c-data-table hover striped sorter footer columnFilter itemsPerPageSelect :items="items" :fields="fields"
              :items-per-page="1000" :active-page="1" :responsive="true" @update:column-filter-value="filterColumn"
              pagination thead-top>
              <template slot="cleaner">
                <label class="mfe-2">Filter: </label>
                <c-input v-model="search" placeholder="type string..." type="text" />
              </template>
              <template slot="thead-top">
                <td style="border-top: none"><strong>Total</strong></td>
                <td style="border-top: none" class="text-xs-right">
                  {{ totalData }}
                </td>
              </template>
              <template #feedback="{ item }">
                <td @click="showMore(item.id)" v-if="!readMore[item.id]">
                  {{ item.feedback.substring(0, 15) + ".." }} <br /><span
                    style="color: blue; cursor: pointer">show</span>
                </td>
                <td @click="showLess(item.id)" v-if="readMore[item.id]">
                  {{ item.feedback }} <br />
                  <span style="color: red; cursor: pointer">hide</span>
                </td>
              </template>
              <template #line="{ item }">
                <td @click="showMoreLines(item.id)" v-if="!readMoreLines[item.id]">
                  {{ item.line.substring(0, 15) + ".." }} <br /><span style="color: blue; cursor: pointer">show</span>
                </td>
                <td @click="showLessLines(item.id)" v-if="readMoreLines[item.id]">
                  {{ item.line }} <br />
                  <span style="color: red; cursor: pointer">hide</span>
                </td>
              </template>
              <template #description="{ item }">
                <td v-if="item.description">
                  <p style="white-space: pre-line">
                    {{ item.description.substring(0, 15) }}...
                  </p>
                </td>
                <td v-else-if="!item.description"></td>
              </template>
              <template #doctors="{ item }">
                <td>
                  <CButton color="primary" variant="outline" square size="sm" @click="showDoctorDetails(item)">
                    Show
                  </CButton>
                </td>
              </template>
              <template #products="{ item }">
                <td>
                  <CButton color="dark" variant="outline" square size="sm" @click="showProductDetails(item)">
                    show
                  </CButton>
                </td>
              </template>
              <template #employees="{ item }">
                <td>
                  <CButton color="success" variant="outline" square size="sm" @click="showEmployeeDetails(item)">
                    show
                  </CButton>
                </td>
              </template>
              <template #approvals="{ item }">
                <td>
                  <CButton color="warning" variant="outline" square size="sm" @click="showApprovalDetails(item)">
                    show
                  </CButton>
                </td>
              </template>
              <template #actions="{ item }">
                <td>
                  <div class="row justify-content-center">
                    <c-button v-if="
                      item.lat != 0 &&
                      item.lng != 0 &&
                      item.lng != null &&
                      item.lng != null
                    " color="warning" class="btn-sm mt-2 mr-1" @click="
                      $mapDialog.open(`Map of Request id: ${item.id}`, [
                        item.position,
                      ])
                      ">
                      <CIcon class="text-white" name="marker" />
                    </c-button>
                    <c-button color="warning" class="btn-sm mt-2 mr-1" v-if="checkPermission('reset_commercial_approvals')"
                      @click="updateApprovalDetails(item)"><c-icon name="cil-loop-circular" /></c-button>
                    <c-button color="primary" class="btn-sm mt-2 mr-1"
                      v-if="checkPermission('show_single_commercial_requests')"
                      :to="{ name: 'Commercial', params: { id: item.id } }">
                      <c-icon name="cil-magnifying-glass" />
                    </c-button>
                    <c-button style="background-color: blueviolet; color: white" class="btn-sm mt-2 mr-1"
                      v-if="checkPermission('show_single_commercial_requests')"
                      :to="{ name: 'PrintCommercial', params: { id: item.id } }">
                      <c-icon name="cil-print" />
                    </c-button>
                    <c-button color="success" class="btn-sm mt-2 mr-1"
                      v-if="checkPermission('edit_commercial_requests')"
                      :to="{ name: 'EditCommercial', params: { id: item.id } }">
                      <c-icon name="cil-pencil" />
                    </c-button>
                    <c-button color="danger" class="btn-sm mt-2 mr-1"
                      v-if="checkPermission('delete_commercial_requests')" @click="
                        $dialog.open(
                            'Delete',
                            'Do you want to delete this record?',
                            {
                              color: 'danger',
                            }
                          )
                          .then((confirmed) => {
                            if (confirmed) {
                              deleteCommercialRequest(item);
                            }
                          })
                        ">
                      <c-icon name="cil-trash" />
                    </c-button>
                  </div>
                </td>
              </template>
              <template #status="{ item }">
                <td>
                  <strong v-if="item.status == 1" style="color: green">Approved</strong>
                  <strong v-if="item.status == 0" style="color: red">Dispproved</strong>
                  <strong v-if="item.status == null" style="color: blue">Pending</strong>
                </td>
              </template>
            </c-data-table>
            <c-pagination v-if="items.length != 0" :activePage.sync="page" @update:activePage="getData()"
              :pages="total" />
          </c-card-body>
        </c-card>
      </transition>
    </c-col>
  </c-row>
</template>
<script>
export default {
  data() {
    return {
      items: [],
      fields: [
        "id",
        "user",
        "type",
        "line",
        "from",
        "to",
        'insertion',
        "products",
        "doctors",
        "employees",
        "total",
        "paid",
        "feedback",
        "approvals",
        "status",
        "description",
        "archived",
        "actions",
      ],
      collapseDuration: 0,
      page: 1,
      total: 0,
      totalData: 0,
      previousTimeout: null,
      previousTimeoutFilter: null,
      search: null,
      readMore: {},
      readMoreLines: {},
      uploadPercentage: 0,
      progressBar: false,
      successModal: false,
      updateModal: false,
      sendModal: false,
      file: "",
      file_name: "",
    };
  },
  methods: {
    showMore(id) {
      this.$set(this.readMore, id, true);
    },
    showLess(id) {
      this.$set(this.readMore, id, false);
    },
    showMoreLines(id) {
      this.$set(this.readMoreLines, id, true);
    },
    showLessLines(id) {
      this.$set(this.readMoreLines, id, false);
    },
    handleFileUpload(files, event) {
      this.file = event.target.files[0];
      this.file_name = event.target.files[0].name;
    },
    importDoctors() {
      this.progressBar = true;
      let formData = new FormData();
      formData.append("file", this.file);
      this.importFile("/api/import/commercial/doctors", formData);
      document.getElementById("file").value = "";
      this.successModal = false;
    },
    showApprovalDetails(item) {
      axios
        .post("/api/commercial-approval-data", item)
        .then((response) => {
          const approvals = response.data.data;
          this.$root.$table("Commercial Approval", approvals);
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    showDoctorDetails(item) {
      axios
        .post("/api/get-doctors-data", item)
        .then((response) => {
          const doctors = response.data.data.doctors;
          const outOfList = response.data.data.outOfList;
          this.$root.$table(
            "Commercial Doctor Details",
            doctors,
            [],
            outOfList
          );
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    showProductDetails(item) {
      axios
        .post("/api/get-products-data", item)
        .then((response) => {
          const products = response.data.data;
          this.$root.$table("Commercial Product Details", products);
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    showEmployeeDetails(item) {
      axios
        .post("/api/get-users-data", item)
        .then((response) => {
          const employees = response.data.data;
          this.$root.$table("Commercial Employee Details", employees);
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    filterColumn(data) {
      this.previousTimeoutFilterClear();
      Object.keys(data).forEach(key => {
        if (!data[key]) {
          delete data[key];
        }
      })
      this.previousTimeoutFilter = setTimeout(() => this.loadAfterFiltering(data), 800)
    },
    loadAfterFiltering(data) {
      axios.post("/api/commercial-request/index?page=" + this.page, {
        columnFilter: data
      })
        .then((res) => {
          this.items = res.data.data.data;
          this.total = res.data.data.last_page;
          this.totalData = res.data.data.total;
          this.items.forEach((item) => {
            item.position = {
              lat: parseFloat(item.lat),
              lng: parseFloat(item.lng),
            };
          });
        })
        .catch((err) => {
          this.showErrorMessage(err);
        });
    },
    getData() {
      axios
        .post("/api/commercial-request/index?page=" + this.page, {
          query: this.search,
        })
        .then((res) => {
          this.items = res.data.data.data;
          this.total = res.data.data.last_page;
          this.totalData = res.data.data.to;
          this.items.forEach((item) => {
            item.position = {
              lat: parseFloat(item.lat),
              lng: parseFloat(item.lng),
            };
          });
        })
        .catch((err) => {
          this.showErrorMessage(err);
        });
    },
    removeCommercialRequest(item) {
      const index = this.items.findIndex((type) => item.id === type.id);
      this.items.splice(index, 1);
    },
    deleteCommercialRequest(item) {
      axios
        .delete(`/api/commercial-request/${item.id}`)
        .then((res) => {
          this.removeCommercialRequest(item);
          this.flash("Commercial Request Deleted Successfully");
        })
        .catch((err) => this.showErrorMessage(err));
    },
    updateApprovalDetails(item) {
      axios
        .post("/api/get-commercial-approvals", item)
        .then((response) => {
          const approvals = response.data.data.approvals;
          const fields = response.data.data.fields;
          const mainApproval = response.data.data.mainApproval;
          this.$root.$requestFlow(
            "Commercial Approval Flow",
            approvals,
            fields,
            mainApproval
          );
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    timeoutClear() {
      if (this.previousTimeout) {
        clearTimeout(this.previousTimeout);
        this.previousTimeout = null;
      }
    },
    previousTimeoutFilterClear() {
      if (this.previousTimeoutFilter) {
        clearTimeout(this.previousTimeoutFilter);
        this.previousTimeoutFilter = null;
      }
    }
  },
  // created() {
  //   this.getData();
  // },
  watch: {
    search() {
      this.timeoutClear();
      this.previousTimeout = setTimeout(() => this.getData(), 500);
    },
  },
};
</script>
