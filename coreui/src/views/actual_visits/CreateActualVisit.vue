<template>
  <div class="professional-container">
    <CRow>
      <CCol col="12">
        <CCard class="professional-card">
          <CCardHeader class="professional-header">
            <div class="header-content">
              <div class="header-title-section">
                <CIcon name="cil-calendar-check" class="header-icon" />
                <h3 class="header-title">Create Actual Visit</h3>
              </div>
              <div class="header-actions">
                <CButton v-if="policies.length > 0"
                  color="info"
                  size="sm"
                  class="header-action-btn info-btn mr-2"
                  @click="showPolicy = !showPolicy">
                  <CIcon name="cil-info" class="header-icon-enhanced" />
                </CButton>
                <CButton
                  color="primary"
                  size="sm"
                  class="header-action-btn location-btn-enhanced mr-2"
                  @click="showCurrentLocation">
                  <div class="btn-icon-wrapper">
                    <CIcon name="cil-location-pin" class="header-icon-crystal" />
                    <div class="icon-glow-effect"></div>
                  </div>
                  <span class="btn-tooltip">Current Location</span>
                </CButton>
                <CButton v-if="actual_visit.account_id"
                  color="success"
                  size="sm"
                  class="header-action-btn map-btn-enhanced"
                  @click="showAccountLocation(actual_visit.account_id)">
                  <div class="btn-icon-wrapper">
                    <CIcon name="cil-map" class="header-icon-crystal text-white" />
                    <div class="icon-glow-effect"></div>
                  </div>
                  <span class="btn-tooltip">Account Location</span>
                </CButton>
              </div>
            </div>
          </CCardHeader>
        <CCardBody class="professional-body">
          <transition name="slide-fade">
            <div class="policy-section" v-if="showPolicy">
              <div class="policy-card">
                <div class="policy-header">
                  <CIcon name="cil-shield-alt" class="policy-icon" />
                  <h5 class="policy-title">Visit Policies</h5>
                </div>
                <div class="policy-list">
                  <div class="policy-item" v-for="(item, index) in policies" :key="index">
                    <CIcon name="cil-check-circle" class="policy-check" />
                    <span>{{ item.policy }}</span>
                  </div>
                </div>
              </div>
            </div>
          </transition>

          <CTabs class="professional-tabs"
                 :active-tab="0"
                 variant="pills"
                 add-tab-classes="nav-justified"
                 add-content-classes="p-4"
                 fade>
            <CTab active>
              <template slot="title">
                <CIcon name="cilDescription" class="custom_icon" /> Visit
                Details
              </template>

              <CCard no-header>
                <CCardBody>
                  <c-row>
                    <c-col lg="4" md="4" sm="8">
                      <CFormGroup>
                        <template #label> Line </template>
                        <template #input>
                          <v-select v-model="actual_visit.line_id" :options="lines" label="name" :value="0"
                            :reduce="(line) => line.id" placeholder="Select Line" class="mt-2"
                            @input="getLineLastLevel()" :disabled="makeDisabled" />
                        </template>
                      </CFormGroup>
                    </c-col>
                    <c-col lg="4" md="4" sm="8" v-if="actual_visit.line_id && divisions.length != 0">
                      <CFormGroup>
                        <template #label> Division </template>
                        <template #input>
                          <v-select required v-model="actual_visit.div_id" :options="divisions" label="name" :value="0"
                            :reduce="(division) => division.id" placeholder="Select Division" class="mt-2"
                            @input="getLineDivisionBricks()" :disabled="makeDisabled" />
                        </template>
                      </CFormGroup>
                    </c-col>
                    <c-col lg="4" md="4" sm="8">
                      <CFormGroup>
                        <template #label> Brick </template>
                        <template #input>
                          <v-select required v-model="actual_visit.brick_id" :options="bricks" label="name" :value="0"
                            :reduce="(brick) => brick.id" placeholder="Select Brick" class="mt-2"
                            :disabled="makeDisabled" @input="applyBrickUpdate()" />
                        </template>
                      </CFormGroup>
                    </c-col>
                    <c-col lg="4" md="4" sm="8" v-if="addShift">
                      <CFormGroup>
                        <template #label> Shift </template>
                        <template #input>
                          <v-select v-model="actual_visit.shift_id" :options="shifts" label="name" :value="0"
                            :reduce="(shift) => shift.id" placeholder="Select Shift" class="mt-2" />
                        </template>
                      </CFormGroup>
                    </c-col>
                    <c-col lg="4" md="4" sm="8" v-if="actual_visit.brick_id">
                      <CFormGroup>
                        <template #label> Account Type </template>
                        <template #input>
                          <v-select v-model="actual_visit.account_type_id" :options="accountTypes" label="name"
                            :value="0" :reduce="(account_type) => account_type.id" placeholder="Select option"
                            class="mt-2" @input="getAccounts()" :disabled="makeDisabled" />
                        </template>
                      </CFormGroup>
                    </c-col>
                    <c-col lg="4" md="4" sm="8"
                      v-if="actual_visit.account_type_id && addPharmacyType && withPharmacyType">
                      <CFormGroup>
                        <template #label> Pharmacy Type </template>
                        <template #input>
                          <v-select v-model="actual_visit.pharmacy_type_id" :options="pharmacyTypes" label="name"
                            :value="0" :reduce="(visitType) => visitType.id" placeholder="Select Visit Type"
                            class="mt-2" />
                        </template>
                      </CFormGroup>
                    </c-col>
                    <c-col lg="4" md="4" sm="8" v-if="actual_visit.account_type_id">
                      <CFormGroup>
                        <template #label> Account </template>
                        <template #input>
                          <v-select v-model="actual_visit.account_id" :options="accounts" label="name" :value="0"
                            :reduce="(account) => account.id" placeholder="Select Account" class="mt-2"
                            @input="getDoctors()" :disabled="makeDisabled" />
                        </template>
                      </CFormGroup>
                    </c-col>
                    <c-col lg="4" md="4" sm="8"
                      v-if="actual_visit.account_id && plan_level == 'Account' && isMultipleDoctors">
                      <CFormGroup>
                        <template #label> Doctor </template>
                        <template #input>
                          <v-select v-model="actual_visit.account_dr_ids" :options="doctors" label="name" :value="0"
                            :reduce="(doctor) => doctor.id" placeholder="Select Doctor" class="mt-2" multiple />
                        </template>
                      </CFormGroup>
                    </c-col>
                    <c-col lg="4" md="4" sm="8"
                      v-if="actual_visit.account_id && plan_level == 'Account' && !isMultipleDoctors">
                      <CFormGroup>
                        <template #label> Doctor </template>
                        <template #input>
                          <v-select v-model="actual_visit.account_dr_id" :options="doctors" label="name" :value="0"
                            :reduce="(doctor) => doctor.id" placeholder="Select Doctor" class="mt-2" />
                        </template>
                      </CFormGroup>
                    </c-col>
                    <c-col lg="4" md="4" sm="8" v-if="actual_visit.account_id && plan_level == 'Doctor'">
                      <CFormGroup>
                        <template #label> Doctor </template>
                        <template #input>
                          <v-select v-model="actual_visit.account_dr_id" :options="doctors" label="name" :value="0"
                            :reduce="(doctor) => doctor.id" placeholder="Select Doctor" class="mt-2"
                            :disabled="doctorDisabled" @input="getDoctorData(actual_visit.account_dr_id)" />
                        </template>
                      </CFormGroup>
                    </c-col>
                    <c-col lg="4" md="4" sm="8" v-if="actual_visit.line_id">
                      <CFormGroup>
                        <template #label> Visit Time </template>
                        <template #input>
                          <datetime type="datetime" class="v_date" format="yyyy-MM-dd HH:mm:ss"
                            :min-datetime="min_line_actual_visit_date" :max-datetime="max_line_actual_visit_date"
                            v-model="actual_visit.visit_date" :disabled="timeDisabled"></datetime>
                        </template>
                      </CFormGroup>
                      <!-- :disabled="timeDisabled" -->
                    </c-col>
                    <c-col lg="4" md="4" sm="8" v-if="actual_visit.line_id">
                      <CFormGroup>
                        <template #label> Visit Type </template>
                        <template #input>
                          <v-select v-model="actual_visit.visit_type_id" :options="visitTypes" label="name" :value="0"
                            :reduce="(visitType) => visitType.id" placeholder="Select Visit Type" class="mt-2"
                            @input="checkVisitType()" />
                        </template>
                      </CFormGroup>
                    </c-col>
                    <c-col lg="4" md="4" sm="8" v-if="double_visit_types.length > 0">
                      <CFormGroup>
                        <template #label> Another Type </template>
                        <template #input>
                          <v-select v-model="actual_visit.double_visit_type_id" :options="double_visit_types"
                            label="name" :value="0" :reduce="(doubleVisitType) => doubleVisitType.id"
                            placeholder="Select Another Type" class="mt-2" />
                        </template>
                      </CFormGroup>
                    </c-col>
                  </c-row>
                </CCardBody>
              </CCard>
              <!-- Enhanced Managers & Actions Card -->
              <div class="enhanced-card-wrapper">
                <transition name="card-slide-up" appear>
                  <div class="enhanced-card">
                    <!-- Managers Section -->
                    <transition name="managers-fade" appear>
                      <div v-if="manager_data.length > 0 && actual_visit.visit_type_id == 2"
                           class="managers-enhanced-section">
                        <div class="managers-enhanced-header">
                          <div class="header-left-section">
                            <div class="header-icon-enhanced">
                              <div class="icon-background-glow"></div>
                              <CIcon name="cil-people" class="managers-enhanced-icon" />
                            </div>
                            <div class="header-content-enhanced">
                              <h4 class="managers-enhanced-title">
                                <span class="title-gradient">Team Managers</span>
                                <div class="title-underline"></div>
                              </h4>
                              <p class="managers-enhanced-subtitle">
                                <CIcon name="cil-info" class="subtitle-icon" />
                                Select managers for this visit
                              </p>
                            </div>
                          </div>
                          <div class="managers-controls-enhanced">
                            <div class="managers-count-enhanced">
                              <div class="count-wrapper">
                                <span class="count-label">Available</span>
                                <span class="count-badge-enhanced">{{ manager_data.length }}</span>
                              </div>
                            </div>
                            <div class="check-all-wrapper-enhanced">
                              <input
                                type="checkbox"
                                id="checkAllManagers"
                                v-model="allManagersSelected"
                                @change="toggleAllManagers"
                                class="check-all-checkbox-enhanced" />
                              <label
                                for="checkAllManagers"
                                class="check-all-label-enhanced">
                                <div class="check-all-indicator-enhanced">
                                  <div class="check-all-background-enhanced"></div>
                                  <CIcon name="cil-check-alt" class="check-all-icon-enhanced" />
                                  <div class="check-all-ripple-enhanced"></div>
                                </div>
                                <span class="check-all-text-enhanced">
                                  <div class="select-text">Select All</div>
                                  <div class="select-subtext">{{ allManagersSelected ? 'Deselect' : 'Choose' }} all managers</div>
                                </span>
                              </label>
                            </div>
                          </div>
                        </div>

                        <div class="managers-grid-enhanced">
                          <transition-group name="manager-card" tag="div" class="managers-container-enhanced">
                            <div v-for="(manager, index) in manager_data"
                                 :key="manager.id"
                                 class="manager-card-enhanced"
                                 :style="{ '--item-index': index }"
                                 :class="{ 'selected': isManagerSelected(manager.id) }">
                              <div class="manager-selection-indicator">
                                <div class="selection-glow"></div>
                              </div>
                              <input
                                type="checkbox"
                                :name="`manager_${manager.id}`"
                                :value="manager.id"
                                :id="`manager_${manager.id}`"
                                @click="_onInputManager"
                                class="manager-checkbox-enhanced" />
                              <label
                                :for="`manager_${manager.id}`"
                                class="manager-label-enhanced">
                                <div class="manager-avatar-enhanced">
                                  <div class="avatar-background-gradient"></div>
                                  <CIcon name="cil-user" class="avatar-icon-enhanced" />
                                  <div class="avatar-status-indicator">
                                    <div class="status-pulse"></div>
                                  </div>
                                </div>
                                <div class="manager-info-enhanced">
                                  <div class="manager-name-wrapper">
                                    <span class="manager-name-enhanced">{{ manager.name }}</span>
                                    <div class="name-highlight-line"></div>
                                  </div>
                                  <div class="manager-role-enhanced">
                                    <div class="role-badge-wrapper">
                                      <CIcon name="cil-star" class="role-icon" />
                                      <strong class="manager-bold-text">MANAGER</strong>
                                    </div>
                                  </div>
                                </div>
                                <div class="checkbox-indicator-enhanced">
                                  <div class="checkbox-background"></div>
                                  <CIcon name="cil-check" class="check-icon-enhanced" />
                                  <div class="checkbox-ripple"></div>
                                </div>
                                <div class="manager-actions-enhanced">
                                  <div class="action-indicator">
                                    <CIcon name="cil-arrow-right" class="action-arrow" />
                                  </div>
                                  <transition name="status-fade">
                                    <div v-if="isManagerSelected(manager.id)" class="selected-badge">
                                      <CIcon name="cil-check-circle" class="selected-icon" />
                                      <span>Selected</span>
                                    </div>
                                  </transition>
                                </div>
                              </label>
                            </div>
                          </transition-group>
                        </div>
                      </div>
                    </transition>

                    <!-- Actions Section -->
                    <div class="actions-enhanced-section">
                      <div class="actions-enhanced-header">
                        <div class="actions-icon-wrapper">
                          <CIcon name="cil-settings" class="actions-icon" />
                        </div>
                        <h4 class="actions-title">Quick Actions</h4>
                      </div>

                      <div class="actions-grid">
                        <!-- Virtual Call Button -->
                        <div class="action-item">
                          <div class="action-card virtual-call-card">
                            <CButton
                              color="primary"
                              @click="show"
                              class="action-btn virtual-call-btn">
                              <div class="btn-content">
                                <CIcon name="cil-user-follow" class="btn-icon" />
                                <span class="btn-text">Virtual Call</span>
                              </div>
                            </CButton>
                          </div>
                        </div>

                        <!-- Doctors Dialog Button -->
                        <div class="action-item" v-if="shift_id">
                          <div class="action-card doctors-card">
                            <CButton
                              color="warning"
                              @click="openDoctorsDialog()"
                              class="action-btn doctors-btn">
                              <div class="btn-content">
                                <CIcon name="cil-description" class="btn-icon" />
                                <span class="btn-text">Add Doctors</span>
                              </div>
                            </CButton>
                          </div>
                        </div>
                      </div>

                      <!-- Add Doctors Component -->
                      <div class="doctors-component-wrapper">
                        <add-doctors ref="addDoctors" @getDoctors="saveVisitedDoctors"></add-doctors>
                      </div>
                    </div>
                  </div>
                </transition>
              </div>
            </CTab>

            <CTab>
              <template slot="title">
                <CIcon name="cilGift" class="custom_icon" /> Giveaways
              </template>

              <CCard no-header>
                <CCardBody>
                  <template v-for="(row, key) in rows">
                    <c-row :key="key">
                      <c-col lg="5" md="5" sm="5">
                        <CFormGroup>
                          <template #label> Giveaway </template>
                          <template #input>
                            <v-select v-model="row.giveaway_id" :options="giveaways" label="name" :value="0"
                              :reduce="(giveaway) => giveaway.id" placeholder="Select Giveaway" class="mt-2" />
                          </template>
                        </CFormGroup>
                      </c-col>

                      <c-col lg="3" md="3" sm="3">
                        <CInput label="No. Of Units" type="number" placeholder="Units" v-model="row.units"></CInput>
                      </c-col>

                      <c-col lg="2" md="2" sm="2" v-if="showBtn">
                        <CButton color="danger" @click="deleteRow(key)" class="btn-sm text-white mt-4 px-2">
                          <CIcon name="cil-trash" />
                        </CButton>
                      </c-col>
                    </c-row>
                  </template>
                  <c-row>
                    <c-col>
                      <c-button class="btn btn-primary btn-xs" @click="addRow()">
                        Add Giveaway
                      </c-button>
                    </c-col>
                  </c-row>
                </CCardBody>
              </CCard>
            </CTab>

            <CTab>
              <template slot="title">
                <CIcon name="cilSpa" class="custom_icon" /> Products
              </template>

              <CCard no-header>
                <CCardBody>
                  <template v-for="(productrow, product) in productrows">
                    <c-row :key="product">
                      <c-col lg="4" md="4" sm="8">
                        <CFormGroup>
                          <template #label>
                            {{ product_brand_level_label }}
                            <strong><span style="color: red">*</span></strong>
                          </template>
                          <template #input>
                            <v-select v-model="productrow.product_id" :options="products" label="name" :value="0"
                              :reduce="(product) => product.id" placeholder="Select Product" class="mt-2"
                              @input="getProductData(productrow)" />
                          </template>
                        </CFormGroup>
                      </c-col>
                      <c-col lg="4" md="4" sm="8">
                        <CFormGroup v-if="productrow.product_id">
                          <template #label> Product Message </template>
                          <template #input>
                            <v-select v-model="productrow.message_id" :options="productrow.messages" label="message"
                              :value="0" :reduce="(message) => message.id" placeholder="Select Message" class="mt-2" />
                          </template>
                        </CFormGroup>
                      </c-col>
                      <c-col lg="4" md="4" sm="8">
                        <CFormGroup v-if="productrow.product_id">
                          <template #label> Presentations </template>
                          <template #input>
                            <v-select v-model="productrow.presentation_id" :options="presentations" label="name"
                              :value="0" :reduce="(presentation) => presentation.id" placeholder="Select Presentation"
                              class="mt-2" @input="getSlides(productrow.presentation_id)" />
                          </template>
                        </CFormGroup>
                      </c-col>
                      <c-col lg="4" md="4" sm="8">
                        <CInput label="Samples" type="number" placeholder="Samples" v-model="productrow.samples">
                        </CInput>
                      </c-col>

                      <c-col lg="4" md="4" sm="8">
                        <CFormGroup>
                          <template #label>
                            Visit Feedback
                            <strong><span style="color: red">*</span></strong>
                          </template>
                          <template #input>
                            <v-select v-model="productrow.vFeedback_id" :options="visitFeedbacks" label="name"
                              :value="0" :reduce="(visitFeedback) => visitFeedback.id" placeholder="Select option"
                              class="mt-2" />
                          </template>
                        </CFormGroup>
                      </c-col>

                      <c-col lg="4" md="4" sm="8">
                        <CTextarea label="Comment" type="text" placeholder="Comment" v-model="productrow.notes">
                        </CTextarea>
                      </c-col>

                      <c-col lg="3" md="3" sm="8">
                        <CTextarea label="Follow-up" type="text" placeholder="Follow-up" v-model="productrow.follow_up">
                        </CTextarea>
                      </c-col>

                      <c-col lg="3" md="3" sm="8">
                        <CTextarea label="Market Feedback" type="text" placeholder="Market Feedback"
                          v-model="productrow.market_feedback"></CTextarea>
                      </c-col>
                      <c-col lg="3" md="3" sm="8" v-if="addOrder">
                        <CInput label="Current Order" type="number" placeholder="Order" v-model="productrow.order">
                        </CInput>
                      </c-col>
                      <c-col lg="3" md="3" sm="8" v-if="addStock">
                        <CInput label="Stock" type="number" placeholder="Stock" v-model="productrow.stock">
                        </CInput>
                      </c-col>

                      <c-col lg="4" md="4" sm="8" v-if="showDeleteProductBtn">
                        <CButton color="danger" @click="deleteProductRow(product)" class="btn-sm text-white px-2">
                          <CIcon name="cil-trash" />
                        </CButton>
                      </c-col>
                    </c-row>
                  </template>
                  <button v-if="showAddProductBtn" class="btn text-white btn-primary btn-xs" @click="addProductRow()">
                    Add Product
                  </button>
                </CCardBody>
                <cool-light-box :items="slides" :index.sync="index" @close="close" :srcMediaType="'mediaType'"
                  :fullScreen="fullScreen">
                </cool-light-box>
              </CCard>
            </CTab>

            <CTab>
              <template slot="title">
                <CIcon name="cil-tags" class="custom_icon" /> Attachment
              </template>
              <c-card>
                <c-card-body>
                  <attachment @getAttachments="attach" />
                </c-card-body>
              </c-card>
            </CTab>
            <CTab v-if="actual_visit.account_dr_id && plan_level == 'Doctor'">
              <template slot="title">
                <CIcon name="cil-tags" class="custom_icon" /> Follow-up
              </template>

              <CCard no-header v-if="lastFollowUps">
                <CCardBody>
                  <template>
                    <c-row v-for="(last, index) in lastFollowUps" :key="index">
                      <c-col v-if="last.product_id && last.follow_up">
                        <strong>Follow Up of {{ last.product }}</strong>
                        <br /><br />
                        <CTextarea type="text" placeholder="Follow-up" v-model="last.follow_up"></CTextarea>
                        <CButton class="text-white" color="primary" @click="DoneFollowUp(last)" style="float: right">
                          Done
                        </CButton>
                      </c-col>
                    </c-row>
                  </template>
                </CCardBody>
              </CCard>
            </CTab>
            <CTab v-if="actual_visit.account_dr_id && lastFeedbacks.length > 0">
              <template slot="title">
                <CIcon name="cil-tags" class="custom_icon" /> Feedbacks
              </template>

              <CCard no-header v-if="lastFeedbacks.length > 0">
                <CCardBody>
                  <c-data-table v-if="lastFeedbacks.length > 0" hover striped sorter footer itemsPerPageSelect
                    :items="lastFeedbacks" :fields="lastFeedbacksFields" :items-per-page="100" :active-page="1"
                    :responsive="true" tableFilter pagination thead-top>
                    <template slot="thead-top">
                      <td style="border-top: none"><strong>Total</strong></td>
                      <td style="border-top: none" class="text-xs-right">
                        {{ lastFeedbacks.length }}
                      </td>
                    </template>
                  </c-data-table>
                </CCardBody>
              </CCard>
            </CTab>
          </CTabs>
        </CCardBody>
        <CCardFooter class="professional-footer">
          <div class="footer-actions">
            <CButton
              color="danger"
              class="cancel-btn"
              @click="goBack">
              <CIcon name="cil-arrow-left" class="mr-2" />
              Cancel
            </CButton>
            <CButton
              color="primary"
              class="create-btn"
              @click="store()">
              <CIcon name="cil-check" class="mr-2" />
              Create Visit
            </CButton>
          </div>
        </CCardFooter>
      </CCard>
    </CCol>
  </CRow>
  </div>
</template>


<script>
// @ts-ignore
import MaskedInput from "vue-text-mask";
import CoolLightBox from "../../components/common/SlideShow/CoolLightBox.vue";
// @ts-ignore
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
// @ts-ignore
import { Datetime } from "vue-datetime";
import Attachment from "../../components/common/Attachment.vue";
import AddDoctors from "../../components/actual/addDoctors.vue";

export default {
  name: "CreateActualVisit",
  components: {
    MaskedInput,
    Attachment,
    vSelect,
    Datetime,
    AddDoctors,
    CoolLightBox,
  },
  data: () => {
    return {
      is_manager: 0,
      doctorDisabled: false,
      withPharmacyType: false,
      double_visit_types: [],
      attachments: [],
      policies: [],
      showPolicy: false,
      rows: [
        {
          giveaway_id: null,
          units: 0,
        },
      ],
      marker: { position: { lat: 0, lng: 0 } },
      messages: [],
      shifts: [],
      pharmacyTypes: [],
      productrows: [
        {
          product_id: null,
          message_id: null,
          presentation_id: null,
          samples: 0,
          order: 0,
          stock: 0,
          vFeedback_id: null,
          notes: "",
          follow_up: "",
          market_feedback: "",
          messages: [],
        },
      ],
      lastFeedbacks: [],
      lastFeedbacksFields: [],
      location: null,
      plan_level: null,
      isMultipleDoctors: false,
      addShift: false,
      addStock: false,
      addOrder: false,
      addPharmacyType: false,
      presentations: [],
      shift_id: null,
      makeDisabled: false,
      socialIsEditing: false,
      line_division_name: "",
      min_line_actual_visit_date: "",
      max_line_actual_visit_date: "",
      product_brand_level_label: "",
      actual_visit: {
        plan_id: "",
        line_id: "",
        div_id: "",
        brick_id: "",
        visit_type_id: "",
        double_visit_type_id: "",
        account_type_id: 0,
        speciality_id: 0,
        account_id: "",
        account_dr_id: null,
        shift_id: null,
        pharmacy_type_id: null,
        account_dr_ids: [],
        visit_date: "",
      },
      visitTypes: [],
      lines: [],
      accountTypes: [],
      divisions: [],
      bricks: [],
      specialities: [],
      accounts: [],
      doctors: [],
      files: [],
      giveaways: [],
      products: [],
      visitFeedbacks: [],
      manager_data: [],
      checkAllManagers: true,
      managersChecked: [],
      allManagersSelected: false,
      showBtn: false,
      showDeleteProductBtn: false,
      timeDisabled: false,
      showAttachBtn: false,
      max_products: "",
      showAddProductBtn: true,
      slides: [],
      timer: new Map(),
      index: null,
      fullScreen: true,
      productPresentation: null,
      needToUpdateEndDate: null,
      needToUpdateVisit: [],
      lastFollowUps: {
        product: "",
        product_id: null,
        follow_up: "",
        visit_id: null,
        flag: 0,
      },
      savedFollowUp: null,
      visitedDoctors: [],
      plan: 1,
    };
  },
  methods: {
    openDoctorsDialog() {
      this.$refs.addDoctors.open();
    },
    saveVisitedDoctors({ doctors }) {
      this.visitedDoctors = doctors;
    },
    getDoctorData(doctor) {
      axios
        .get(`/api/get-doctor-data/${doctor}`)
        .then((response) => {
          this.lastFollowUps = response.data.data.followup;
          this.lastFeedbacks = response.data.data.feedbacks;
          this.lastFeedbacksFields = this.lastFeedbacks.length > 0 ? Object.keys(response.data.data.feedbacks[0]) : [];
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    DoneFollowUp(last) {
      last.flag = 1;
      axios
        .post(`/api/save-followups`, {
          last,
        })
        .then(() => {
          this.flash("Follow Up Done By Doctor");
          this.getDoctorData(this.actual_visit.account_dr_id);
          last = null;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    addRow() {
      this.rows.splice(this.rows.length + 1, 0, {});
      this.showBtn = true;
    },
    deleteRow(index) {
      this.rows.splice(index, 1);
      if (this.rows.length == 1) {
        this.showBtn = false;
      }
    },
    checkProductsCount() {
      if (this.productrows.length == this.max_products) {
        this.showAddProductBtn = false;
      } else {
        this.showAddProductBtn = true;
      }
    },
    addProductRow() {
      if (this.productrows.length <= this.max_products - 1) {
        this.productrows.splice(this.productrows.length + 1, 0, {
          product_id: null,
          message_id: null,
          presentation_id: null,
          samples: 0,
          order: 0,
          stock: 0,
          vFeedback_id: null,
          notes: "",
          follow_up: "",
          market_feedback: "",
          messages: [],
        });
        this.showDeleteProductBtn = true;
        this.checkProductsCount();
      }
    },
    deleteProductRow(index) {
      this.productrows.splice(index, 1);
      if (this.productrows.length == 1) {
        this.showDeleteProductBtn = false;
      }
      this.checkProductsCount();
    },
    attach({ files }) {
      for (var i = 0; i < files.length; i++) {
        this.files.push(files[i]);
      }
    },
    goBack() {
      this.$router.go(-1);
    },
    getProductData(productrow) {
      this.productPresentation = productrow.product_id;
      axios
        .get(`/api/get-product-data/${productrow.product_id}`)
        .then((response) => {
          productrow.messages = response.data.product_messages;
          this.presentations = response.data.product_presentations;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
      if (this.actual_visit.plan_id) this.getMapMessage(this.marker.position);
    },
    async getSlides(presentation) {
      try {
        const res = await axios.get(`/api/presentations/${presentation}`);
        this.slides = res.data.slides;
        if (this.slides.length > 0) {
          this.index = 0;
        } else {
          this.showErrorMessage("There is no slides to show.");
        }
      } catch (err) {
        this.showErrorMessage(err);
      }
    },
    close() {
      this.index = null;
      this.fullScreen = false;
      // send request to update statistics with visit_id
    },
    async store() {
      let formData = new FormData();
      if (this.files.length > 0) {
        this.files.forEach((file) => {
          formData.append("attachments[]", file);
        });
        formData.append("countFiles", this.files.length);
        formData.append("folder", "actual");
        await axios
          .post("/api/upload/attachments", formData)
          .then((res) => {
            this.attachments = res.data.data.url;
          })
          .catch((error) => {
            this.showErrorMessage(error);
          });
      }
      if (this.plan_level == 'Account' && this.isMultipleDoctors) {
        await axios
          .post("/api/actual/visits/accounts", {
            plan_id: this.$route.params.id ?? "",
            line_id: this.actual_visit.line_id,
            div_id: this.actual_visit.div_id,
            brick_id: this.actual_visit.brick_id,
            visit_type_id: this.actual_visit.visit_type_id,
            double_visit_type_id: this.actual_visit.double_visit_type_id,
            account_type_id: this.actual_visit.account_type_id,
            account_id: this.actual_visit.account_id,
            plan_level: this.plan_level,
            shift_id: this.actual_visit.shift_id,
            pharmacy_type_id: this.actual_visit.pharmacy_type_id,
            account_dr_ids: this.actual_visit.account_dr_ids,
            visitedDoctors: this.visitedDoctors,
            visit_date: this.crmDateFormat(this.actual_visit.visit_date),
            ll: this.marker.position.lat,
            lg: this.marker.position.lng,
            managers: this.managersChecked,
            level: this.product_brand_level_label,
            giveaways: this.rows.filter(
              (giveaway) => giveaway.giveaway_id != null
            ),
            slides: this.needToUpdateVisit,
            products: this.productrows,
            attachments: this.attachments,
          })
          .then((response) => {
            if (
              this.makeDisabled === true &&
              response.data.status === "success"
            ) {
              this.$router.push({ name: `Main Dashboard` });
            } else {
              if (response.data.status === "failed") {
                this.message = response.data.message;
                this.showAlert();
              } else if (response.data.status === "success") {
                this.flash("Actual Visit Created Successfully");
                this.actual_visit.line_id = "";
                this.actual_visit.div_id = "";
                this.actual_visit.brick_id = "";
                this.actual_visit.visit_type_id = "";
                this.actual_visit.account_type_id = "";
                this.actual_visit.shift_id = "";
                this.actual_visit.speciality_id = "";
                this.actual_visit.account_id = "";
                this.actual_visit.account_dr_ids = [];
                this.actual_visit.visit_date = "";
                this.visitedDoctors = [];
                this.rows = [
                  {
                    giveaway_id: null,
                    units: 0,
                  },
                ];
                this.productrows = [
                  {
                    product_id: null,
                    message_id: null,
                    samples: 0,
                    vFeedback_id: null,
                    notes: "",
                    follow_up: "",
                    market_feedback: "",
                  },
                ];
                this.files = [];
                this.manager_data = [];
                this.needToUpdateVisit = [];
                this.managersChecked = [];
                document.getElementById("files").value = "";
                this.showBtn = false;
                this.showDeleteProductBtn = false;
              }
            }
          })
          .catch((error) => {
            this.showErrorMessage(error);
          });
      } else {
        await axios
          .post("/api/actual_visits", {
            plan_id: this.$route.params.id ?? "",
            line_id: this.actual_visit.line_id,
            div_id: this.actual_visit.div_id,
            brick_id: this.actual_visit.brick_id,
            visit_type_id: this.actual_visit.visit_type_id,
            double_visit_type_id: this.actual_visit.double_visit_type_id,
            account_type_id: this.actual_visit.account_type_id,
            speciality_id: this.actual_visit.speciality_id,
            shift_id: this.actual_visit.shift_id,
            pharmacy_type_id: this.actual_visit.pharmacy_type_id,
            account_id: this.actual_visit.account_id,
            plan_level: this.plan_level,
            account_dr_id: this.actual_visit.account_dr_ids.length > 0
              ? this.actual_visit.account_dr_ids[0] : this.actual_visit.account_dr_id,
            account_dr_ids: this.actual_visit.account_dr_ids,
            visitedDoctors: this.visitedDoctors,
            visit_date: this.crmDateFormat(this.actual_visit.visit_date),
            ll: this.marker.position.lat,
            lg: this.marker.position.lng,
            managers: this.managersChecked,
            level: this.product_brand_level_label,
            giveaways: this.rows.filter(
              (giveaway) => giveaway.giveaway_id != null
            ),
            slides: this.needToUpdateVisit,
            products: this.productrows,
            attachments: this.attachments,
          })
          .then((response) => {
            if (
              this.makeDisabled === true &&
              response.data.status === "success"
            ) {
              this.$router.push({ name: `Main Dashboard` });
            } else {
              if (response.data.status === "failed") {
                this.message = response.data.message;
                this.showAlert();
              } else if (response.data.status === "success") {
                this.flash("Actual Visit Created Successfully");
                this.actual_visit.line_id = "";
                this.actual_visit.div_id = "";
                this.actual_visit.brick_id = "";
                this.actual_visit.visit_type_id = "";
                this.actual_visit.account_type_id = "";
                this.actual_visit.speciality_id = "";
                this.actual_visit.account_id = "";
                this.actual_visit.shift_id = "";
                this.actual_visit.account_dr_id = "";
                this.actual_visit.account_dr_ids = [];
                this.actual_visit.visit_date = "";
                this.visitedDoctors = [];
                this.rows = [
                  {
                    giveaway_id: null,
                    units: 0,
                  },
                ];
                this.productrows = [
                  {
                    product_id: null,
                    message_id: null,
                    samples: 0,
                    vFeedback_id: null,
                    notes: "",
                    follow_up: "",
                    market_feedback: "",
                  },
                ];
                this.files = [];
                this.manager_data = [];
                this.needToUpdateVisit = [];
                this.managersChecked = [];
                document.getElementById("files").value = "";
                this.showBtn = false;
                this.showDeleteProductBtn = false;
              }
            }
          })
          .catch((error) => {
            this.showErrorMessage(error);
          });
      }

    },
    checkVisitType() {
      axios
        .get(`/api/get-visit-another-types/${this.actual_visit.visit_type_id}`)
        .then((response) => {
          this.double_visit_types = response.data.data;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
      if (this.actual_visit.visit_type_id === 2) {
        axios
          .get(`/api/getManagers/${this.actual_visit.line_id}`)
          .then((response) => {
            this.manager_data = response.data.manager_data;
          })
          .catch((error) => {
            this.showErrorMessage(error);
          });
      }
    },
    selectAllManagers() {
      if (document.getElementById("managers-all").checked) {
        var items = document.getElementsByClassName("managers");
        for (var i = 0; i < items.length; ++i) {
          items[i].childNodes[0].checked = true;
          this._allInputManagers(parseInt(items[i].childNodes[0].value));
        }
      } else {
        var items = document.getElementsByClassName("managers");
        for (var i = 0; i < items.length; ++i) {
          {
            items[i].childNodes[0].checked = false;
            this._allInputManagers(parseInt(items[i].childNodes[0].value));
          }
        }
      }
    },
    _allInputManagers: function (needle) {
      if (
        document.querySelector("input[name='manager_" + needle + "']").checked
      ) {
        this.managersChecked.push(needle);
      } else {
        const index = this.managersChecked.indexOf(needle);
        if (index > -1) {
          this.managersChecked.splice(index, 1);
        }
      }
    },
    _onInputManager: function ($event) {
      var needle = parseInt($event.target.value);
      if (
        document.querySelector("input[name='manager_" + needle + "']").checked
      ) {
        this.managersChecked.push(needle);
      } else {
        const index = this.managersChecked.indexOf(needle);
        if (index > -1) {
          this.managersChecked.splice(index, 1);
        }
      }
      this.checkSelectAllManagers();
    },
    checkManagers(id) {
      if (this.managersChecked.includes(id)) {
        return true;
      }
      return false;
    },
    uncheckAllManagers() {
      this.checkAllManagers = false;
      this.managersChecked = [];
      var myEle = document.getElementById("managers-all");
      if (this.checkAllManagers) {
        if (myEle) {
          myEle.checked = true;
        }
      } else {
        if (myEle) {
          myEle.checked = false;
        }
      }
      var items = document.getElementsByClassName("managers");
      for (var i = 0; i < items.length; ++i) {
        items[i].childNodes[0].checked = false;
      }
    },
    toggleAllManagers() {
      if (this.allManagersSelected) {
        // Select all managers
        this.managersChecked = this.manager_data.map(manager => manager.id);
        // Check all individual checkboxes
        this.manager_data.forEach(manager => {
          const checkbox = document.getElementById(`manager_${manager.id}`);
          if (checkbox) {
            checkbox.checked = true;
          }
        });
      } else {
        // Deselect all managers
        this.managersChecked = [];
        // Uncheck all individual checkboxes
        this.manager_data.forEach(manager => {
          const checkbox = document.getElementById(`manager_${manager.id}`);
          if (checkbox) {
            checkbox.checked = false;
          }
        });
      }
    },
    isManagerSelected(managerId) {
      return this.managersChecked.includes(managerId);
    },
    toggleManagerSelection(managerId) {
      const checkbox = document.getElementById(`manager_${managerId}`);
      if (checkbox) {
        checkbox.click();
      }
    },
    checkSelectAllManagers() {
      this.checkAllManagers = true;
      var items = document.getElementsByClassName("managers");
      for (var i = 0; i < items.length; ++i) {
        if (!items[i].childNodes[0].checked) {
          this.checkAllManagers = false;
          break;
        }
      }
      var myEle = document.getElementById("managers-all");
      if (this.checkAllManagers) {
        if (myEle) {
          myEle.checked = true;
        }
      } else {
        if (myEle) {
          myEle.checked = false;
        }
      }
    },
    getLineLastLevel() {
      axios
        .get(`/api/get-line-division/${this.actual_visit.line_id}/${this.plan}`)
        .then((response) => {
          var input_list = document.querySelectorAll(".vdatetime-input");
          for (var i = 0; i < input_list.length; ++i) {
            input_list[i].classList.add("form-control");
            input_list[i].classList.add("mt-2");
          }
          this.actual_visit.div_id = "";
          this.actual_visit.brick_id = "";
          this.actual_visit.account_type_id = "";
          this.actual_visit.account_id = "";
          this.actual_visit.account_dr_id = "";
          this.min_line_actual_visit_date =
            response.data.min_line_actual_visit_date;
          this.max_line_actual_visit_date =
            response.data.max_line_actual_visit_date;
          this.actual_visit.visit_date = response.data.visit_date;
          this.divisions = response.data.divisions;
          this.bricks = response.data.bricks;
          if (!this.is_manager) this.actual_visit.div_id = this.divisions[0].id;
          this.plan_level = response.data.plan_level;
          this.isMultipleDoctors = response.data.isMultipleDoctors;
          this.addShift = response.data.addShift;
          this.addStock = response.data.addStock;
          this.addOrder = response.data.addOrder;
          this.addPharmacyType = response.data.addPharmacyType;
          this.shifts = response.data.shifts;
          this.pharmacyTypes = response.data.pharmacyTypes;
          this.getLineDivisionBricks();
          this.product_brand_level_label =
            response.data.product_brand_level_label;
          this.specialities = response.data.specialities;
          this.giveaways = response.data.giveaways;
          this.products = response.data.products;
          if (!this.actual_visit.plan_id)
            this.getMapMessage(this.marker.position);
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getLineDivisionBricks() {
      axios
        .post(
          `/api/getLineDivisionBricks`, {
          line_id: this.actual_visit.line_id,
          div_id: this.actual_visit.div_id,
        }
        )
        .then((response) => {
          this.getProducts();
          this.bricks = response.data.bricks;
          this.bricks.unshift({ id: "All", name: "All" });
          this.actual_visit.div_id =
            this.actual_visit.div_id === 0 ? 0 : this.actual_visit.div_id;
          if (!this.is_manager) this.actual_visit.brick_id = "All";
          this.actual_visit.account_type_id = "";
          this.actual_visit.account_id = "";
          this.actual_visit.account_dr_id = "";
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getProducts() {
      axios
        .post(
          `/api/get-products`, {
          line_id: this.actual_visit.line_id,
          div_id: this.actual_visit.div_id,
        }
        )
        .then((response) => {
          this.products = response.data.data;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getAccounts() {
      axios
        .post(
          `/api/getAccounts`, {
          line_id: this.actual_visit.line_id,
          div_id: this.actual_visit.div_id,
          brick_id: this.actual_visit.brick_id,
          account_type_id: this.actual_visit.account_type_id,
        }
        )
        .then((response) => {
          this.actual_visit.account_id = "";
          this.shift_id = response.data.isAmShift;
          this.withPharmacyType = response.data.withPharmacyType;
          this.accounts = response.data.accounts;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    applyBrickUpdate() {
      this.actual_visit.account_type_id = "";
      this.actual_visit.account_id = "";
      this.actual_visit.account_dr_id = "";
      if (this.actual_visit.brick_id != 'All' && this.is_manager == 1) {
        axios
          .post(
            `/api/division-from-brick`, {
            line_id: this.actual_visit.line_id,
            brick_id: this.actual_visit.brick_id,
          }
          )
          .then((response) => {
            this.actual_visit.div_id = response.data.data;
          })
          .catch((error) => {
            this.showErrorMessage(error);
          });
      }
    },
    getDoctors() {
      axios
        .get(
          `/api/getDoctors/${this.actual_visit.line_id}/${this.actual_visit.account_id}/${this.actual_visit.div_id}`
        )
        .then((response) => {
          this.actual_visit.account_dr_id = "";
          this.doctors = response.data.doctors;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    geolocate() {
      if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition((position) => {
          this.marker.position = {
            lat: position.coords.latitude,
            lng: position.coords.longitude,
          };
        });
        // this.getMapMessage(this.marker.position);
      } else {
        this.showErrorMessage("Location is not supported");
      }
    },
    show() { },
    showCurrentLocation() {
      this.geolocate();
      if (
        this.marker.position.lat != 0 &&
        this.marker.position.lng != 0
      ) {
        this.$mapDialog.open(`Map of Actual`, [
          this.marker.position
        ]);
      } else {
        this.flash("There is no current location");
      }

    },
    showAccountLocation(account_id) {
      axios
        .post("/api/get-account-location", {
          id: account_id,
          line_id: this.actual_visit.line_id,
          div_id: this.actual_visit.div_id,
        })
        .then((response) => {
          this.location = response.data.data;
          if (this.location.lat != 0 && this.location.lng != 0) {
            this.$mapDialog.open(`Map of Account id: ${this.location.account_id}`, [
              this.location,
            ]);
          } else {
            this.flash("There is no loaction for this account");
          }
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getMapMessage(position) {
      axios
        .post(`/api/get-message-map`, {
          position,
        })
        .then((response) => {
          if (response.data.data.location_setting === "yes") {
            const message = response.data.data.data.message;
            if (response.data.data.data.color === "red")
              this.flash(message, "error");
            else this.flash(message);
          }
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    addStatistics(data) {
      return axios.post("/api/insert-statistics", data);
    },
    getPolicies() {
      axios
        .get("/api/actual-visit-policies/")
        .then((response) => {
          this.policies = response.data.data.policies;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },

    async updateStatistics(id, date) {
      return axios.put(`/api/update-statistics/${id}`, { end: date });
      // console.log(`update ${date}`)
    },
    async updateAndInsert(value1) {
      await this.updateStatistics(this.needToUpdateEndDate, this.getDateNow());
      this.needToUpdateEndDate = (
        await this.addStatistics({
          product_id: this.productPresentation,
          presentation_id: this.slides[value1].presentation_id,
          slide_id: this.slides[value1].id,
          start: this.getDateNow(),
          end: null,
          rate: null,
          comment: null,
        })
      ).data.data;
      this.needToUpdateVisit.push(this.needToUpdateEndDate);
    },
    async slideWatcher(newVal, oldVal) {
      const now = this.getDateNow();
      if (oldVal == null && newVal != null) {
        //presentation is started now
        this.needToUpdateEndDate = (
          await this.addStatistics({
            product_id: this.productPresentation,
            presentation_id: this.slides[newVal].presentation_id,
            slide_id: this.slides[newVal].id,
            start: now,
            end: null,
            rate: null,
            comment: null,
          })
        ).data.data;
        this.needToUpdateVisit.push(this.needToUpdateEndDate);
      } else if (newVal == null && oldVal != null) {
        //presentation is ended now)
        await this.updateStatistics(this.needToUpdateEndDate, now);
      } else {
        await this.updateAndInsert(newVal);
      }
    },
  },
  created() {
    if (this.$route.params.id) {
      axios
        .get(
          `/api/actual_visits/create_plan_actual_visit/${this.$route.params.id}`
        )
        .then((response) => {
          this.makeDisabled = true;
          this.doctorDisabled = response.data.plan.account_dr_id ? true : false;
          this.lines = response.data.lines;
          this.accountTypes = response.data.accountTypes;
          this.is_manager = response.data.is_manager;
          this.timeDisabled = response.data.dateDisabled;
          this.visitTypes = response.data.visitTypes;
          this.visitFeedbacks = response.data.visitFeedbacks;
          this.plan_level = response.data.plan_level;
          this.isMultipleDoctors = response.data.isMultipleDoctors;
          this.max_products = response.data.max_products;
          this.divisions = response.data.divisions;
          this.line_division_name = response.data.line_division_name;
          this.specialities = response.data.specialities;
          this.bricks = response.data.bricks;
          this.bricks.unshift({ id: "All", name: "All" });
          this.giveaways = response.data.giveaways;
          this.products = response.data.products;
          this.pharmacyTypes = response.data.pharmacyTypes;
          this.withPharmacyType = response.data.withPharmacyType;
          this.addPharmacyType = response.data.addPharmacyType;
          this.product_brand_level_label =
            response.data.product_brand_level_label;
          this.accounts = response.data.accounts;
          this.doctors = response.data.doctors;
          this.actual_visit.plan_id = response.data.plan.id;
          this.actual_visit.line_id = response.data.plan.line_id;
          this.actual_visit.div_id = response.data.plan.div_id;
          this.actual_visit.brick_id = "All";
          this.actual_visit.account_type_id = response.data.account_type_id;
          // this.actual_visit.visit_type_id = response.data.plan.visit_type;
          this.actual_visit.account_id = response.data.plan.account_id;
          this.actual_visit.account_dr_id = response.data.plan.account_dr_id;
          if (this.actual_visit.account_dr_id) this.getDoctorData(this.actual_visit.account_dr_id);
          this.actual_visit.visit_date = response.data.plan_visit_date;
          this.min_line_actual_visit_date =
            response.data.min_line_actual_visit_date;
          this.max_line_actual_visit_date =
            response.data.max_line_actual_visit_date;
          setTimeout(() => {
            var input_list = document.querySelectorAll(".vdatetime-input");
            for (var i = 0; i < input_list.length; ++i) {
              input_list[i].classList.add("form-control");
              input_list[i].classList.add("mt-2");
            }
          }, 500);
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    } else {
      axios
        .get(`/api/actual_visits/create`)
        .then((response) => {
          this.lines = response.data.lines;
          this.actual_visit.line_id = this.lines[0].id;
          this.getLineLastLevel();
          this.accountTypes = response.data.accountTypes;
          this.is_manager = response.data.is_manager;
          this.timeDisabled = response.data.dateDisabled;
          this.visitTypes = response.data.visitTypes;
          this.visitFeedbacks = response.data.visitFeedbacks;
          this.max_products = response.data.max_products;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    }
  },
  watch: {
    index: {
      handler: "slideWatcher",
    },
  },
  mounted() {
    this.geolocate();
    this.getPolicies();
  },
};
</script>
<style scoped>
.bounce-enter-active {
  animation: bounce-in 0.5s;
}

.bounce-leave-active {
  animation: bounce-in 0.5s reverse;
}

@keyframes bounce-in {
  0% {
    transform: scale(0);
  }

  50% {
    transform: scale(1.25);
  }

  100% {
    transform: scale(1);
  }
}
/* Professional Container */
.professional-container {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  min-height: 100vh;
  padding: 20px;
}

/* Professional Card */
.professional-card {
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  border: none;
  overflow: hidden;
  background: white;
}

/* Professional Header */
.professional-header {
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  border: none;
  padding: 20px 30px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title-section {
  display: flex;
  align-items: center;
}

.header-icon {
  color: white;
  font-size: 1.5rem;
  margin-right: 12px;
}

.header-title {
  color: white;
  margin: 0;
  font-weight: 600;
  font-size: 1.4rem;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  border-radius: 8px;
  border: 2px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* Professional Body */
.professional-body {
  padding: 30px;
  background: #fafbfc;
}

/* Policy Section */
.policy-section {
  margin-bottom: 30px;
}

.policy-card {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border-radius: 12px;
  padding: 20px;
  color: white;
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.3);
}

.policy-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.policy-icon {
  font-size: 1.3rem;
  margin-right: 10px;
}

.policy-title {
  margin: 0;
  font-weight: 600;
  color: white;
}

.policy-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.policy-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
}

.policy-check {
  margin-right: 10px;
  color: #10b981;
  font-size: 1rem;
}

/* Professional Tabs */
.professional-tabs {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

/* Professional Footer */
.professional-footer {
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  border: none;
  padding: 20px 30px;
}

.footer-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.cancel-btn {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.create-btn {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.cancel-btn:hover,
.create-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* Animations */
.slide-fade-enter-active {
  transition: all 0.3s ease;
}

.slide-fade-leave-active {
  transition: all 0.3s ease;
}

.slide-fade-enter,
.slide-fade-leave-to {
  transform: translateY(-10px);
  opacity: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .professional-container {
    padding: 10px;
  }

  .professional-header {
    padding: 15px 20px;
  }

  .header-content {
    flex-direction: column;
    gap: 15px;
  }

  .professional-body {
    padding: 20px;
  }

  .footer-actions {
    flex-direction: column;
    gap: 10px;
  }

  .cancel-btn,
  .create-btn {
    width: 100%;
    justify-content: center;
  }
}

/* Enhanced Card Styling */
.enhanced-card-wrapper {
  margin: 25px 0;
}

.enhanced-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 20px;
  padding: 30px;
  box-shadow: 0 15px 50px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  position: relative;
  overflow: hidden;
}

.enhanced-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 5px;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
}

/* Enhanced Actions Section */
.actions-enhanced-section {
  margin-top: 30px;
}

.actions-enhanced-header {
  display: flex;
  align-items: center;
  margin-bottom: 25px;
  padding-bottom: 20px;
  border-bottom: 2px solid #f1f5f9;
}

.actions-icon-wrapper {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  width: 55px;
  height: 55px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  box-shadow: 0 8px 25px rgba(240, 147, 251, 0.4);
  animation: icon-pulse 2s infinite;
}

.actions-icon {
  color: white;
  font-size: 1.6rem;
}

.actions-title {
  color: #1a202c;
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
  letter-spacing: -0.025em;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 25px;
}

.action-item {
  position: relative;
}

.action-card {
  background: white;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
  border: 2px solid #e2e8f0;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.action-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.6s ease;
}

.action-card:hover {
  transform: translateY(-6px);
  box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
  border-color: #667eea;
}

.action-card:hover::before {
  left: 100%;
}

.virtual-call-card {
  border-left: 4px solid #667eea;
}

.doctors-card {
  border-left: 4px solid #f59e0b;
}

.action-btn {
  width: 100%;
  border: none;
  border-radius: 12px;
  padding: 15px 20px;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.virtual-call-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
}

.doctors-btn {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  box-shadow: 0 6px 20px rgba(245, 158, 11, 0.3);
}

.action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.btn-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.btn-icon {
  font-size: 1.3rem;
}

.btn-text {
  font-weight: 600;
  letter-spacing: 0.025em;
}

.doctors-component-wrapper {
  margin-top: 20px;
  padding: 20px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

/* Enhanced Animations */
@keyframes icon-pulse {
  0%, 100% {
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
  }
  50% {
    box-shadow: 0 8px 35px rgba(102, 126, 234, 0.6);
  }
}

@keyframes badge-bounce {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Card Animations */
.card-slide-up-enter-active {
  transition: all 0.8s cubic-bezier(0.23, 1, 0.32, 1);
}

.card-slide-up-enter {
  opacity: 0;
  transform: translateY(50px);
}

.managers-fade-enter-active {
  transition: all 0.6s ease;
}

.managers-fade-enter {
  opacity: 0;
  transform: translateY(20px);
}

.manager-card-enter-active {
  transition: all 0.5s cubic-bezier(0.23, 1, 0.32, 1);
}

.manager-card-leave-active {
  transition: all 0.3s cubic-bezier(0.755, 0.05, 0.855, 0.06);
}

.manager-card-enter {
  opacity: 0;
  transform: translateY(30px) scale(0.9);
}

.manager-card-leave-to {
  opacity: 0;
  transform: translateY(-30px) scale(0.9);
}

.manager-card-move {
  transition: transform 0.4s ease;
}

/* Enhanced Manager Styles */
.manager-name-enhanced {
  font-weight: 800 !important;
  color: #1a202c !important;
  font-size: 1.3rem !important;
  margin-bottom: 8px !important;
  transition: all 0.4s ease !important;
  letter-spacing: -0.025em !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
}

.manager-role-enhanced {
  color: #64748b !important;
  font-size: 1rem !important;
  font-weight: 500 !important;
  transition: all 0.4s ease !important;
}

.manager-bold-text {
  font-weight: 900 !important;
  color: #4f46e5 !important;
  font-size: 1.1rem !important;
  letter-spacing: 0.05em !important;
  text-transform: uppercase !important;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  background-clip: text !important;
  animation: text-shimmer 2s ease-in-out infinite !important;
  position: relative !important;
  display: inline-block !important;
}

.manager-bold-text::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transform: scaleX(0);
  transition: transform 0.3s ease;
  border-radius: 1px;
}

.manager-label-enhanced:hover .manager-bold-text::after {
  transform: scaleX(1);
}

/* Enhanced Checkbox Styles */
.checkbox-indicator-enhanced {
  width: 32px !important;
  height: 32px !important;
  border: 3px solid #d1d5db !important;
  border-radius: 10px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
  background: white !important;
  position: relative !important;
  overflow: hidden !important;
  cursor: pointer !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

.checkbox-indicator-enhanced:hover {
  transform: scale(1.1) !important;
  border-color: #667eea !important;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3) !important;
}

.checkbox-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transform: scale(0);
  transition: transform 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  border-radius: 7px;
}

.checkbox-ripple {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(102, 126, 234, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.4s ease;
}

.checkbox-indicator-enhanced:active .checkbox-ripple {
  width: 60px;
  height: 60px;
  opacity: 0;
}

.check-icon-enhanced {
  color: white !important;
  font-size: 1.2rem !important;
  opacity: 0 !important;
  transform: scale(0) rotate(-45deg) !important;
  transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55) !important;
  z-index: 2 !important;
  position: relative !important;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3)) !important;
}

/* Enhanced Checked State */
.manager-checkbox-enhanced:checked + .manager-label-enhanced .checkbox-indicator-enhanced {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
  border-color: #10b981 !important;
  box-shadow: 0 6px 20px rgba(16, 185, 129, 0.5) !important;
  transform: scale(1.05) !important;
}

.manager-checkbox-enhanced:checked + .manager-label-enhanced .checkbox-background {
  transform: scale(1) !important;
}

.manager-checkbox-enhanced:checked + .manager-label-enhanced .check-icon-enhanced {
  opacity: 1 !important;
  transform: scale(1) rotate(0deg) !important;
}

.manager-checkbox-enhanced:checked + .manager-label-enhanced .manager-name-enhanced,
.manager-checkbox-enhanced:checked + .manager-label-enhanced .manager-role-enhanced {
  color: white !important;
}

.manager-checkbox-enhanced:checked + .manager-label-enhanced .manager-bold-text {
  color: white !important;
  -webkit-text-fill-color: white !important;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3) !important;
}

/* Text Shimmer Animation */
@keyframes text-shimmer {
  0% {
    background-position: -200% center;
  }
  100% {
    background-position: 200% center;
  }
}

/* Pulse Animation for Manager Cards */
@keyframes manager-pulse {
  0%, 100% {
    box-shadow: 0 8px 30px rgba(102, 126, 234, 0.2);
  }
  50% {
    box-shadow: 0 12px 40px rgba(102, 126, 234, 0.4);
  }
}

.manager-card-enhanced:hover {
  animation: manager-pulse 2s infinite;
}

/* Floating Animation for Selected Cards */
@keyframes float {
  0%, 100% {
    transform: translateY(-6px) scale(1.02);
  }
  50% {
    transform: translateY(-10px) scale(1.02);
  }
}

.manager-checkbox-enhanced:checked + .manager-label-enhanced {
  animation: float 3s ease-in-out infinite;
}

/* Enhanced Header Action Buttons */
.header-action-btn {
  background: rgba(255, 255, 255, 0.15) !important;
  border: 2px solid rgba(255, 255, 255, 0.3) !important;
  border-radius: 12px !important;
  padding: 10px 12px !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  backdrop-filter: blur(10px) !important;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1) !important;
  position: relative !important;
  overflow: hidden !important;
}

.header-action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.header-action-btn:hover::before {
  left: 100%;
}

.header-action-btn:hover {
  transform: translateY(-2px) scale(1.05) !important;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2) !important;
  border-color: rgba(255, 255, 255, 0.6) !important;
  background: rgba(255, 255, 255, 0.25) !important;
}

.header-action-btn:active {
  transform: translateY(0) scale(0.98) !important;
}

/* Specific Button Colors */
.info-btn:hover {
  background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%) !important;
  border-color: #06b6d4 !important;
  box-shadow: 0 8px 25px rgba(6, 182, 212, 0.4) !important;
}

.location-btn:hover {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%) !important;
  border-color: #3b82f6 !important;
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4) !important;
}

.map-btn:hover {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
  border-color: #10b981 !important;
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4) !important;
}

/* Enhanced Header Icons */
.header-icon-enhanced {
  font-size: 1.2rem !important;
  color: white !important;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3)) !important;
  transition: all 0.3s ease !important;
}

.header-action-btn:hover .header-icon-enhanced {
  transform: scale(1.1) !important;
  filter: drop-shadow(0 3px 6px rgba(0, 0, 0, 0.4)) !important;
}

/* Icon Glow Effects */
.info-btn:hover .header-icon-enhanced {
  color: white !important;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.8) !important;
}

.location-btn:hover .header-icon-enhanced {
  color: white !important;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.8) !important;
}

.map-btn:hover .header-icon-enhanced {
  color: white !important;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.8) !important;
}

/* Pulse Animation for Header Icons */
@keyframes header-icon-pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.header-action-btn:hover .header-icon-enhanced {
  animation: header-icon-pulse 1.5s ease-in-out infinite;
}

/* Enhanced Header Actions Container */
.header-actions {
  display: flex !important;
  gap: 8px !important;
  align-items: center !important;
}

/* Improved Visibility */
.header-action-btn {
  min-width: 44px !important;
  min-height: 44px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* Focus States for Accessibility */
.header-action-btn:focus {
  outline: 3px solid rgba(255, 255, 255, 0.5) !important;
  outline-offset: 2px !important;
}

/* Active States */
.header-action-btn:active .header-icon-enhanced {
  transform: scale(0.95) !important;
}

/* Enhanced Professional Button Styles */
.location-btn-enhanced,
.map-btn-enhanced {
  background: rgba(255, 255, 255, 0.95) !important;
  border: 2px solid rgba(255, 255, 255, 0.8) !important;
  border-radius: 16px !important;
  padding: 12px 16px !important;
  min-width: 50px !important;
  min-height: 50px !important;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
  backdrop-filter: blur(20px) !important;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8) !important;
  position: relative !important;
  overflow: hidden !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.location-btn-enhanced::before,
.map-btn-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent);
  transition: left 0.6s ease;
}

.location-btn-enhanced:hover::before,
.map-btn-enhanced:hover::before {
  left: 100%;
}

/* Location Button Specific Styles */
.location-btn-enhanced {
  background: linear-gradient(135deg,
    rgba(59, 130, 246, 0.95) 0%,
    rgba(37, 99, 235, 0.95) 100%) !important;
  border-color: rgba(59, 130, 246, 0.8) !important;
  box-shadow:
    0 8px 32px rgba(59, 130, 246, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
}

.location-btn-enhanced:hover {
  background: linear-gradient(135deg,
    rgba(37, 99, 235, 1) 0%,
    rgba(29, 78, 216, 1) 100%) !important;
  border-color: rgba(37, 99, 235, 1) !important;
  box-shadow:
    0 12px 40px rgba(59, 130, 246, 0.5),
    inset 0 1px 0 rgba(255, 255, 255, 0.4) !important;
  transform: translateY(-3px) scale(1.05) !important;
}

/* Map Button Specific Styles */
.map-btn-enhanced {
  background: linear-gradient(135deg,
    rgba(16, 185, 129, 0.95) 0%,
    rgba(5, 150, 105, 0.95) 100%) !important;
  border-color: rgba(16, 185, 129, 0.8) !important;
  box-shadow:
    0 8px 32px rgba(16, 185, 129, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
}

.map-btn-enhanced:hover {
  background: linear-gradient(135deg,
    rgba(5, 150, 105, 1) 0%,
    rgba(4, 120, 87, 1) 100%) !important;
  border-color: rgba(5, 150, 105, 1) !important;
  box-shadow:
    0 12px 40px rgba(16, 185, 129, 0.5),
    inset 0 1px 0 rgba(255, 255, 255, 0.4) !important;
  transform: translateY(-3px) scale(1.05) !important;
}

/* Button Icon Wrapper */
.btn-icon-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

/* Crystal Clear Icons */
.header-icon-crystal {
  font-size: 1.4rem !important;
  color: white !important;
  filter:
    drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3))
    drop-shadow(0 0 8px rgba(255, 255, 255, 0.5)) !important;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
  z-index: 2 !important;
  position: relative !important;
}

.location-btn-enhanced:hover .header-icon-crystal,
.map-btn-enhanced:hover .header-icon-crystal {
  transform: scale(1.15) !important;
  filter:
    drop-shadow(0 3px 6px rgba(0, 0, 0, 0.4))
    drop-shadow(0 0 12px rgba(255, 255, 255, 0.8))
    drop-shadow(0 0 20px rgba(255, 255, 255, 0.4)) !important;
  animation: icon-sparkle 1.5s ease-in-out infinite !important;
}

/* Icon Glow Effect */
.icon-glow-effect {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 30px;
  height: 30px;
  background: radial-gradient(circle,
    rgba(255, 255, 255, 0.6) 0%,
    rgba(255, 255, 255, 0.2) 50%,
    transparent 100%);
  border-radius: 50%;
  transform: translate(-50%, -50%) scale(0);
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1;
}

.location-btn-enhanced:hover .icon-glow-effect,
.map-btn-enhanced:hover .icon-glow-effect {
  transform: translate(-50%, -50%) scale(1.5);
  animation: glow-pulse 2s ease-in-out infinite;
}

/* Button Tooltips */
.btn-tooltip {
  position: absolute;
  bottom: -35px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 6px 12px;
  border-radius: 8px;
  font-size: 0.75rem;
  font-weight: 600;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  z-index: 1000;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.btn-tooltip::before {
  content: '';
  position: absolute;
  top: -5px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-bottom: 5px solid rgba(0, 0, 0, 0.9);
}

.location-btn-enhanced:hover .btn-tooltip,
.map-btn-enhanced:hover .btn-tooltip {
  opacity: 1;
  visibility: visible;
  bottom: -40px;
}

/* Professional Card Background Harmony */
.professional-header {
  background: linear-gradient(135deg,
    #667eea 0%,
    #764ba2 50%,
    #667eea 100%) !important;
  position: relative !important;
}

.professional-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg,
    rgba(255, 255, 255, 0.1) 0%,
    transparent 50%,
    rgba(255, 255, 255, 0.1) 100%);
  animation: header-shimmer 3s ease-in-out infinite;
}

/* Enhanced Animations */
@keyframes icon-sparkle {
  0%, 100% {
    filter:
      drop-shadow(0 3px 6px rgba(0, 0, 0, 0.4))
      drop-shadow(0 0 12px rgba(255, 255, 255, 0.8))
      drop-shadow(0 0 20px rgba(255, 255, 255, 0.4));
  }
  50% {
    filter:
      drop-shadow(0 3px 6px rgba(0, 0, 0, 0.4))
      drop-shadow(0 0 16px rgba(255, 255, 255, 1))
      drop-shadow(0 0 24px rgba(255, 255, 255, 0.6));
  }
}

@keyframes glow-pulse {
  0%, 100% {
    opacity: 0.6;
    transform: translate(-50%, -50%) scale(1.5);
  }
  50% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(2);
  }
}

@keyframes header-shimmer {
  0%, 100% {
    opacity: 0.3;
  }
  50% {
    opacity: 0.6;
  }
}

/* Active States for Enhanced Buttons */
.location-btn-enhanced:active,
.map-btn-enhanced:active {
  transform: translateY(-1px) scale(0.98) !important;
}

.location-btn-enhanced:active .header-icon-crystal,
.map-btn-enhanced:active .header-icon-crystal {
  transform: scale(1.05) !important;
}

/* Enhanced Managers Grid Styles */
.managers-grid-enhanced {
  margin-top: 24px;
}

.managers-container-enhanced {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 20px;
  padding: 0;
}

.manager-card-enhanced {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 20px;
  padding: 24px;
  border: 2px solid #e2e8f0;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  cursor: pointer;
  box-shadow:
    0 4px 20px rgba(0, 0, 0, 0.08),
    0 1px 3px rgba(0, 0, 0, 0.05);
  animation: manager-card-enter 0.6s ease-out;
  animation-delay: calc(var(--item-index, 0) * 0.1s);
  animation-fill-mode: both;
}

.manager-card-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  transform: scaleX(0);
  transition: transform 0.3s ease;
  transform-origin: left;
}

.manager-card-enhanced:hover::before {
  transform: scaleX(1);
}

.manager-card-enhanced:hover {
  transform: translateY(-8px);
  box-shadow:
    0 20px 40px rgba(102, 126, 234, 0.15),
    0 8px 16px rgba(102, 126, 234, 0.1);
  border-color: #667eea;
}

.manager-card-enhanced.selected {
  background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
  border-color: #10b981;
  box-shadow:
    0 8px 32px rgba(16, 185, 129, 0.2),
    0 4px 12px rgba(16, 185, 129, 0.1);
}

.manager-card-enhanced.selected::before {
  background: linear-gradient(90deg, #10b981 0%, #059669 50%, #047857 100%);
  transform: scaleX(1);
}

.manager-card-enhanced.selected .manager-name-enhanced {
  color: #065f46 !important;
  font-weight: 800;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.manager-card-enhanced.selected .manager-bold-text {
  color: #047857 !important;
  font-weight: 800;
}

.manager-card-enhanced.selected .role-department {
  color: #064e3b !important;
  font-weight: 600;
}

.manager-selection-indicator {
  position: absolute;
  top: -2px;
  right: -2px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  opacity: 0;
  transform: scale(0);
  transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  z-index: 10;
}

.manager-card-enhanced.selected .manager-selection-indicator {
  opacity: 1;
  transform: scale(1);
}

.selection-glow {
  position: absolute;
  inset: -4px;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border-radius: 50%;
  opacity: 0.3;
  filter: blur(6px);
  animation: selection-pulse 2s infinite;
}

.manager-checkbox-enhanced {
  position: absolute;
  opacity: 0;
  pointer-events: none;
}

.manager-label-enhanced {
  display: flex;
  align-items: center;
  cursor: pointer;
  margin: 0;
  width: 100%;
  position: relative;
  z-index: 2;
}

.manager-avatar-enhanced {
  width: 64px;
  height: 64px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  position: relative;
  box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
  overflow: hidden;
}

.manager-card-enhanced:hover .manager-avatar-enhanced {
  transform: scale(1.05);
  box-shadow: 0 12px 28px rgba(102, 126, 234, 0.4);
}

.avatar-background-gradient {
  position: absolute;
  inset: -2px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  border-radius: 20px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.manager-card-enhanced:hover .avatar-background-gradient {
  opacity: 0.8;
}

.avatar-icon-enhanced {
  font-size: 28px;
  color: white;
  z-index: 2;
  position: relative;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.avatar-status-indicator {
  position: absolute;
  bottom: 4px;
  right: 4px;
  width: 16px;
  height: 16px;
  background: #10b981;
  border: 3px solid white;
  border-radius: 50%;
  z-index: 3;
}

.status-pulse {
  position: absolute;
  inset: -2px;
  background: #10b981;
  border-radius: 50%;
  animation: status-pulse 2s infinite;
}

.manager-info-enhanced {
  flex: 1;
  min-width: 0;
}

.manager-name-wrapper {
  position: relative;
  margin-bottom: 8px;
}

.manager-name-enhanced {
  font-size: 18px;
  font-weight: 700;
  color: #1f2937;
  margin: 0;
  line-height: 1.3;
  transition: all 0.3s ease;
}

.manager-card-enhanced:hover .manager-name-enhanced {
  color: #667eea;
}

.name-highlight-line {
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  transition: width 0.3s ease;
}

.manager-card-enhanced:hover .name-highlight-line {
  width: 100%;
}

.manager-role-enhanced {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.role-badge-wrapper {
  display: flex;
  align-items: center;
  gap: 6px;
}

.role-icon {
  font-size: 14px;
  color: #f59e0b;
}

.manager-bold-text {
  font-size: 14px;
  font-weight: 700;
  color: #667eea;
  letter-spacing: 0.5px;
}

.role-department {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
}

/* Enhanced Check All Managers Styles */
.managers-controls-enhanced {
  display: flex;
  align-items: center;
  gap: 20px;
}

.check-all-wrapper-enhanced {
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 16px;
  padding: 12px 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 2px solid #e2e8f0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.check-all-wrapper-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
  transition: left 0.6s ease;
}

.check-all-wrapper-enhanced:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(102, 126, 234, 0.2);
  border-color: #667eea;
}

.check-all-wrapper-enhanced:hover::before {
  left: 100%;
}

.check-all-checkbox-enhanced {
  position: absolute;
  opacity: 0;
  pointer-events: none;
}

.check-all-label-enhanced {
  display: flex;
  align-items: center;
  cursor: pointer;
  margin: 0;
  font-weight: 600;
  color: #374151;
  transition: all 0.3s ease;
}

.check-all-indicator-enhanced {
  width: 36px;
  height: 36px;
  border: 3px solid #d1d5db;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  background: white;
  position: relative;
  overflow: hidden;
  margin-right: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.check-all-indicator-enhanced:hover {
  transform: scale(1.1);
  border-color: #667eea;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.check-all-background-enhanced {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transform: scale(0);
  transition: transform 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  border-radius: 9px;
}

.check-all-ripple-enhanced {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(102, 126, 234, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.4s ease;
}

.check-all-indicator-enhanced:active .check-all-ripple-enhanced {
  width: 70px;
  height: 70px;
  opacity: 0;
}

.check-all-icon-enhanced {
  color: white;
  font-size: 1.4rem;
  opacity: 0;
  transform: scale(0) rotate(-45deg);
  transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  z-index: 2;
  position: relative;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

.check-all-text-enhanced {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.select-text {
  font-size: 1rem;
  font-weight: 700;
  letter-spacing: 0.025em;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  transition: all 0.3s ease;
}

.select-subtext {
  font-size: 0.75rem;
  font-weight: 500;
  color: #6b7280;
  transition: all 0.3s ease;
}

/* Enhanced Checked State for Check All */
.check-all-checkbox-enhanced:checked + .check-all-label-enhanced .check-all-indicator-enhanced {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border-color: #10b981;
  box-shadow: 0 6px 20px rgba(16, 185, 129, 0.5);
  transform: scale(1.05);
}

.check-all-checkbox-enhanced:checked + .check-all-label-enhanced .check-all-background-enhanced {
  transform: scale(1);
}

.check-all-checkbox-enhanced:checked + .check-all-label-enhanced .check-all-icon-enhanced {
  opacity: 1;
  transform: scale(1) rotate(0deg);
}

.check-all-checkbox-enhanced:checked + .check-all-label-enhanced .check-all-text-enhanced {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 1px 3px rgba(16, 185, 129, 0.3);
}

.check-all-checkbox-enhanced:checked + .check-all-label-enhanced {
  color: #059669;
}

/* Hover Effects for Check All */
.check-all-wrapper-enhanced:hover .check-all-text-enhanced {
  animation: text-glow 2s ease-in-out infinite;
}

/* Enhanced Animations for Check All */
@keyframes text-glow {
  0%, 100% {
    text-shadow: 0 0 5px rgba(102, 126, 234, 0.3);
  }
  50% {
    text-shadow: 0 0 10px rgba(102, 126, 234, 0.6);
  }
}

@keyframes check-all-pulse {
  0%, 100% {
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.2);
  }
  50% {
    box-shadow: 0 8px 30px rgba(102, 126, 234, 0.4);
  }
}

.check-all-wrapper-enhanced:hover {
  animation: check-all-pulse 2s infinite;
}

/* Responsive Design for Check All */
@media (max-width: 768px) {
  .managers-controls-enhanced {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .check-all-wrapper-enhanced {
    justify-content: center;
    padding: 15px 20px;
  }

  .check-all-text-enhanced {
    font-size: 1.1rem;
  }
}

/* Enhanced Checkbox Styles for Manager Cards */
.checkbox-indicator-enhanced {
  width: 32px;
  height: 32px;
  border: 2px solid #d1d5db;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: white;
  position: relative;
  overflow: hidden;
  margin-left: auto;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.checkbox-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  transform: scale(0);
  transition: transform 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  border-radius: 8px;
}

.check-icon-enhanced {
  color: white;
  font-size: 1.2rem;
  opacity: 0;
  transform: scale(0) rotate(-45deg);
  transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  z-index: 2;
  position: relative;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

.checkbox-ripple {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(16, 185, 129, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.3s ease;
}

.checkbox-indicator-enhanced:active .checkbox-ripple {
  width: 60px;
  height: 60px;
  opacity: 0;
}

/* Checked State for Manager Cards */
.manager-checkbox-enhanced:checked + .manager-label-enhanced .checkbox-indicator-enhanced {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border-color: #10b981;
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.4);
  transform: scale(1.05);
}

.manager-checkbox-enhanced:checked + .manager-label-enhanced .checkbox-background {
  transform: scale(1);
}

.manager-checkbox-enhanced:checked + .manager-label-enhanced .check-icon-enhanced {
  opacity: 1;
  transform: scale(1) rotate(0deg);
}

/* Manager Actions Enhanced */
.manager-actions-enhanced {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  margin-left: 12px;
}

.action-indicator {
  width: 28px;
  height: 28px;
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.action-background {
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.manager-card-enhanced:hover .action-background {
  opacity: 1;
}

.action-arrow {
  font-size: 14px;
  color: #6b7280;
  transition: all 0.3s ease;
  z-index: 2;
  position: relative;
}

.manager-card-enhanced:hover .action-arrow {
  color: white;
  transform: translateX(2px);
}

.selected-badge {
  display: flex;
  align-items: center;
  gap: 4px;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
  animation: selected-badge-enter 0.3s ease-out;
}

.selected-icon {
  font-size: 10px;
}

/* Enhanced Animations */
@keyframes manager-card-enter {
  0% {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes selection-pulse {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.1);
  }
}

@keyframes status-pulse {
  0%, 100% {
    opacity: 0.8;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

@keyframes selected-badge-enter {
  0% {
    opacity: 0;
    transform: scale(0.8) translateY(-10px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.status-fade-enter-active, .status-fade-leave-active {
  transition: all 0.3s ease;
}

.status-fade-enter, .status-fade-leave-to {
  opacity: 0;
  transform: scale(0.8);
}

/* Manager Card Transitions */
.manager-card-enter-active {
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.manager-card-leave-active {
  transition: all 0.3s ease;
}

.manager-card-enter {
  opacity: 0;
  transform: translateY(30px) scale(0.9);
}

.manager-card-leave-to {
  opacity: 0;
  transform: translateY(-10px) scale(1.05);
}

/* Responsive Design for Manager Cards */
@media (max-width: 768px) {
  .managers-container-enhanced {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .manager-card-enhanced {
    padding: 20px;
  }

  .manager-avatar-enhanced {
    width: 56px;
    height: 56px;
    margin-right: 12px;
  }

  .avatar-icon-enhanced {
    font-size: 24px;
  }

  .manager-name-enhanced {
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .manager-card-enhanced {
    padding: 16px;
  }

  .manager-avatar-enhanced {
    width: 48px;
    height: 48px;
    margin-right: 10px;
  }

  .avatar-icon-enhanced {
    font-size: 20px;
  }

  .manager-name-enhanced {
    font-size: 15px;
  }

  .manager-actions-enhanced {
    margin-left: 8px;
  }
}
</style>
