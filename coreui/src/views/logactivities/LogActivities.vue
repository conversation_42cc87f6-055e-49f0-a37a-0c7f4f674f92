<template>
  <CRow>
    <CCol col="12" xl="12">
      <transition name="slide">
        <CCard>
          <CCardHeader> Log Activities </CCardHeader>
          <CCardBody>
            <CDataTable
              hover
              striped
              sorter
              tableFilter
              footer
              itemsPerPageSelect
              :items="items"
              :fields="fields"
              :items-per-page="1000"
              :active-page="1"
              :responsive="true"
              pagination
            >
              <template #actions="{ item }">
                <td>
                  <div class="row justify-content-center">
                    <c-button
                      v-if="item.action_id == 5 && !isHidden && !item.is_exist"
                      color="success"
                      class="btn-sm mt-2 mr-1 text-white"
                      @click="
                        $dialog.open(
                            'Restore',
                            'Do you want to Restore this record?',
                            {
                              color: 'success',
                            }
                          )
                          .then((confirmed) => {
                            if (confirmed) {
                              restore(item.id);
                            }
                          })
                      "
                      ><CIcon name="cilActionUndo"
                    /></c-button>
                    <c-button
                      v-if="item.action_id == 5 && !isHidden && !item.is_exist"
                      color="danger"
                      class="btn-sm mt-2 mr-1"
                      @click="
                        $dialog.open(
                            'Restore',
                            'Do you want to Delete this record forever?',
                            {
                              color: 'danger',
                            }
                          )
                          .then((confirmed) => {
                            if (confirmed) {
                              forceDelete(item.id);
                            }
                          })
                      "
                      ><CIcon name="cil-grid-slash"
                    /></c-button>
                    <c-button
                      v-if="
                        item.position.lat != null && item.position.lng != null
                      "
                      color="primary"
                      class="btn-sm mt-2 mr-1"
                      @click="
                        $mapDialog.open(`Map of log Activity id: ${item.id}`, [
                          item.position,
                        ])
                      "
                      ><CIcon class="text-white" name="marker" />
                    </c-button>
                  </div>
                </td>
              </template>
              <template #imported_path="{ item }">
                <td>
                  <div class="row justify-content-center">
                    <a
                      v-if="item.action_id == 10"
                      style="color: blue"
                      @click="uploadedDownload(item.id)"
                      >{{ item.imported_path }}</a
                    >
                  </div>
                </td>
              </template>
              <template #imported_name="{ item }">
                <td>
                  <div
                    v-if="item.action_id == 10"
                    class="row justify-content-center"
                  >
                    {{ item.imported_name }}
                  </div>
                </td>
              </template>
            </CDataTable>
            <c-pagination
              v-if="items.length != 0"
              :activePage.sync="page"
              @update:activePage="loadPage()"
              :pages="total"
            />
          </CCardBody>
        </CCard>
      </transition>
    </CCol>
  </CRow>
</template>

<script>
export default {
  name: "Log-activity",
  data() {
    return {
      items: [],
      fields: [
        "id",
        "ip",
        "user_id",
        "user",
        "full_name",
        "permission",
        "model_id",
        "model_type",
        // "ll",
        // "lg",
        "creation_date",
        "imported_path",
        "imported_name",
        "actions",
      ],
      isHidden: false,
      showModal: false,
      position: { lat: 0, lng: 0 },
      page: 1,
      total: 0,
    };
  },
  methods: {
    uploadedDownload(id) {
      this.exportFile(`/api/upload-download/${id}`, "imported.xlsx");
    },
    restore(id) {
      axios
        .post(`/api/restore/${id}`)
        .then((response) => {
          this.isHidden = true;
          this.flash("Data Successfully Restored.");
          this.getData();
          this.isHidden = false;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    forceDelete(id) {
      axios
        .post(`/api/force-delete/${id}`)
        .then((response) => {
          this.isHidden = true;
          this.flash("Data Successfully Deleted Forever.");
          this.getData();
          this.isHidden = false;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    loadPage() {
      axios.get("/api/logActivities?page=" + this.page).then((res) => {
        this.items = res.data.data.data;
        this.total = res.data.data.last_page;
      });
    },
    getData() {
      axios
        .get("/api/logActivities")
        .then((res) => {
          this.items = res.data.data.data;
        })
        .catch((err) => {
          this.showErrorMessage(err);
        });
    },
    sendPosition(position) {
      this.position = position;
      this.showModal = true;
    },
  },
  created() {
    this.getData();
  },
};
</script>
