<template>
  <div style="
      overflow: hidden;
      background-color: white;
      width: 720px;
      margin: auto;
      border-radius: 7px;
    ">
    <div style="float: left" class="card">
      <img :src="url" style="width: 100%; height: 70%" />
      <div style="padding:20px">
        <table>
          <tbody>
            <tr>
              <td><label>Mobile:</label></td>
              <td class="blue">
                <label>{{ user.mobile }}</label>
              </td>
            </tr>
            <tr>
              <td><label>Status:</label></td>
              <td class="blue">
                <label>{{ user.status }}</label>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    <div style="float: left" class="cardDetails">
      <div style="padding-left: 20px; padding-top: 20px">
        <label style="font-weight: bold">User Details:</label>
        <table>
          <tbody>
            <tr>
              <td><label>Name:</label></td>
              <td class="blue">
                <label>{{ user.name }}</label>
              </td>
            </tr>
            <tr>
              <td><label>Full Name:</label></td>
              <td class="blue">
                <label>{{ user.full_name }}</label>
              </td>
            </tr>
            <tr>
              <td><label>Email:</label></td>
              <td class="blue">
                <label>{{ user.email }}</label>
              </td>
            </tr>
            <tr>
              <td><label>Personal Email:</label></td>
              <td class="blue">
                <label>{{ user.personal_email }}</label>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="about" style="border-radius: 7px">
        <label style="font-weight: bold">System Details:</label>
        <table>
          <tbody>
            <tr v-for="(detail, index) in details" :key="index">
              <td>
                <label>{{ detail.line.name }}</label>
              </td>
              <td>
                <label>{{ detail.name }}</label>
              </td>
              <td>
                <c-button color="primary" class="btn-sm mt-2 mr-1" @click="getLocation(detail)">
                  <CIcon class="text-white" name="marker" />
                </c-button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      user: null,
      url: null,
      details: [],
    };
  },
  methods: {
    initialize() {
      axios
        .get("/api/user-profile")
        .then((response) => {
          this.user = response.data.user;
          this.url = response.data.user.image;
          this.details = response.data.user_divisions;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getLocation(division) {
      let position = {
        lat: division.ll,
        lng: division.lg,
      }
      this.$mapDialog.open(`Map of Division id: ${this.division.id}`, [
        position,
      ]);
    }
  },
  created() {
    this.initialize();
  },
};
</script>
<style scoped>
.card {
  width: 270px;
  height: 400px;
  font-family: arial;
  border: none;
}

.cardDetails {
  width: 450px;
  height: 400px;
  border: none;
}

div {
  border: none;
}

table {
  display: table;
  white-space: nowrap;
  width: 100%;
}

td {
  text-align: left;
  font-size: 17px;
}

tr {
  padding-top: 40px;
}

.blue {
  color: rgb(15, 15, 231);
  font-size: 17px;
}

.about {
  width: 80%;
  margin-left: 20px;
  margin-top: 30px;
  padding: 20px;
}

label {
  font-size: 17px;
}
</style>
